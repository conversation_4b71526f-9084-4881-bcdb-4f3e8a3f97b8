<p align="center">
    <img alt="logo" src="https://wot-design-uni.cn/logo.png" width="200">
</p>
<h1 align="center">Wot UI</h1>

<div align="center">
<p><a href="./README.md">简体中文</a> | English</p>
</div>

<p align="center">📱 A uni-app component library built with Vue3 + TypeScript, inspired by <a href="https://github.com/jd-ftf/wot-design-mini?tab=readme-ov-file">wot-design</a></p>

<p align="center">

<a href="https://github.com/Moonofweisheng/wot-design-uni">
  <img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/Moonofweisheng/wot-design-uni?logo=github&color=%234d80f0&link=https%3A%2F%2Fgithub.com%2FMoonofweisheng%2Fwot-design-uni&style=flat-square">
 </a>


<a href="https://github.com/Moonofweisheng/wot-design-uni">
  <img alt="GitHub" src="https://img.shields.io/codecov/c/github/Moonofweisheng/wot-design-uni?style=flat-square">
 </a>

<a href="https://www.npmjs.com/package/wot-design-uni">
  <img alt="npm" src="https://img.shields.io/npm/dm/wot-design-uni?logo=npm&link=https%3A%2F%2Fwww.npmjs.com%2Fpackage%2Fwot-design-uni&style=flat-square">
</a>

 <a href="https://www.npmjs.com/package/wot-design-uni">
  <img alt="npm" src="https://img.shields.io/npm/v/wot-design-uni?logo=npm&color=%234d80f0&link=https%3A%2F%2Fwww.npmjs.com%2Fpackage%2Fwot-design-uni&style=flat-square">
</a>

<a href="https://github.com/actions-cool/" target="_blank" referrerpolicy="no-referrer">
  <img src="https://img.shields.io/badge/using-actions--cool-red?style=flat-square" alt="actions-cool" />
</a>

<a href="https://app.netlify.com/sites/wot-design-uni/deploys" target="_blank" referrerpolicy="no-referrer">
  <img src="https://api.netlify.com/api/v1/badges/0991d8a9-0fb0-483b-8961-5bde066bbd50/deploy-status" alt="deploy-status" />
</a>

</p>

<p align="center">
  🚀 <a href="https://wot-design-uni.cn">Documentation (Recommended)</a>&nbsp;
  ✈️ <a href="https://wot-design-uni.pages.dev/">Documentation (Cloudflare)</a>&nbsp;
  🔥 <a href="https://wot-design-uni.netlify.app/">Documentation (Netlify)</a>&nbsp;
</p>

## ✨ Features

- 🎯 Multi-platform support: WeChat Mini Program, Alipay Mini Program, DingTalk Mini Program, H5, APP, etc.
- 🚀 70+ high-quality components covering mainstream mobile scenarios.
- 💪 Built with TypeScript, providing a robust component type system.
- 🌍 Internationalization support with 15 built-in language packs.
- 📖 Rich documentation and component examples.
- 🎨 Theme customization through CSS variables.
- 🍭 Dark mode support.

## 📱 Preview

Scan the QR code to view the demo. Note: Due to WeChat review restrictions, the current WeChat Mini Program demo may not be the latest version. You can clone the code locally for preview.

<p style="display:flex;gap:24px">
<img src="https://wot-design-uni.cn/wx.jpg" width="200" height="200"/>
<img src="https://wot-design-uni.cn/alipay.png" width="200" height="200" />
<img src="https://wot-design-uni.cn/h5.png" width="200" height="200" />
<img src="https://wot-design-uni.cn/android.png" width="200" height="200" />
</p>

## Quick Start

For detailed instructions, see [Quick Start](https://wot-design-uni.cn/guide/quick-use.html).

## Links

- [FAQ](https://wot-design-uni.cn/guide/common-problems.html)
- [Changelog](https://wot-design-uni.cn/guide/changelog.html)
- [Discussions](https://github.com/Moonofweisheng/wot-design-uni/discussions)
- [QQ Group](https://wot-design-uni.cn/guide/join-group.html)

## Showcase

Check out some excellent [examples](https://wot-design-uni.cn/guide/cases.html) here!

We welcome contributions of excellent demos and cases. Feel free to submit your case in this [issue](https://github.com/Moonofweisheng/wot-design-uni/issues/16).

## Contributing

Please read our [Contributing Guide](./.github/CONTRIBUTING.md) before making changes to the code.

If you encounter any issues, please feel free to submit an [Issue](https://github.com/Moonofweisheng/wot-design-uni/issues). We also welcome your [Pull Requests](https://github.com/Moonofweisheng/wot-design-uni/pulls).

## Contributors

Thanks to all the [developers](https://github.com/Moonofweisheng/wot-design-uni/graphs/contributors) who have contributed to Wot UI.

<a href="https://github.com/Moonofweisheng/wot-design-uni/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=Moonofweisheng/wot-design-uni" />
</a>

## Donate

Developing a UI component library is time-consuming work, especially when it needs to be adapted for multiple platforms. The Wot UI team often works late into the night...

If you find Wot UI helpful in your development work, you can donate to support our development work. Any amount is welcome, even if it's just a cola.

After donating, your nickname and message will be displayed on the [donor list](https://wot-design-uni.cn/reward/donor.html).

### Afdian Donation

<a href="https://afdian.com/a/weisheng233">https://afdian.com/a/weisheng233</a>

### Scan to Donate

<p>
<img src="https://wot-design-uni.cn/weixinQrcode.jpg" width="200" height="200" style="margin-right:30px"/>
<img src="https://wot-design-uni.cn/alipayQrcode.jpg" width="200" height="200" />
</p>

## Acknowledgments

- [wot-design](https://github.com/jd-ftf/wot-design-mini) - Thanks to the wot-design team for their years of maintenance.
- [uni-helper](https://github.com/uni-helper) - Thanks to the uni-helper team for providing uni-app tool libraries.
- [Donors](https://wot-design-uni.cn/reward/donor.html) - Thanks to all donors who help Wot UI grow better.

## License

This project is licensed under the [MIT License](https://en.wikipedia.org/wiki/MIT_License). Feel free to enjoy and participate in open source.

[![Star History Chart](https://api.star-history.com/svg?repos=Moonofweisheng/wot-design-uni&type=Date)](https://star-history.com/#Moonofweisheng/wot-design-uni&Date)
