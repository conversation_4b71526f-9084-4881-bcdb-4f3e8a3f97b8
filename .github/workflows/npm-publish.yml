name: Publish to npm
on:
  push:
    tags:
      - 'v*' # Push events to matching v*, i.e. v1.0, v20.15.10

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          registry-url: https://registry.npmjs.org

      - name: Install Dependencies
        run: |
          npm install pnpm -g
          pnpm install

      - name: Run Build WEB TYPES
        run: pnpm build:web-types

      - name: Run Compiler
        run: pnpm compiler

      - name: Publish Package
        run: |
          cd lib
          npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_PUBLISH_TOKEN }}