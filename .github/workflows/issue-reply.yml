name: Issue Reply

on:
  issues:
    types: [labeled]

jobs:
  issue-reply:
    runs-on: ubuntu-latest
    steps:
      - name: pending
        if: github.event.label.name == 'pending'
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          issue-number: ${{ github.event.issue.number }}
          body: |
            你好，@${{ github.event.issue.user.login }}。

            看起来这条 issue 描述得有些模糊，缺少一些必要的信息，推荐阅读一下[贡献指南](https://github.com/Moonofweisheng/wot-design-uni/blob/master/.github/CONTRIBUTING.md)。

      - name: need reproduce
        if: github.event.label.name == 'need reproduce'
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          issue-number: ${{ github.event.issue.number }}
          body: |
            你好，@${{ github.event.issue.user.login }}。

            我们需要你提供一个最小重现demo，以便于我们帮你排查问题。你可以通过 [create-uni](https://github.com/uni-helper/create-uni) 快速创建一个wot-design-uni项目，并添加相关复现逻辑来提供。
      - name: help wanted
        if: github.event.label.name == 'help wanted'
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            你好，@${{ github.event.issue.user.login }}。

            我们完全同意你的提议/反馈，欢迎直接在此仓库创建一个 Pull Request 来解决这个问题。请将 Pull Request 发到正确的分支，务必填写 Pull Request 内的预设模板，提供改动所需相应的 示例、TypeScript 定义、文档等，并确保在各平台测试通过，我们会尽快进行 Review，提前感谢和期待您的贡献。
    