name: Upload To <PERSON>xinMiniProgram

on:
  push:
    tags:
      - 'v*' # Push events to matching v*, i.e. v1.0, v20.15.10

  # 允许手动触发工作流
  workflow_dispatch:
    inputs:
      version:
        description: '版本号 (例如: 1.0.0)'
        required: true
        type: string
        default: ''
      description:
        description: '版本描述'
        required: false
        type: string
        default: '手动发布版本'
      branch:
        description: '要使用的分支'
        required: false
        type: string
        default: 'master'

jobs:
  upload:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.branch || 'master' }}
    - shell: bash
      env:
        PRIVATE_KEY: ${{ secrets.MP_PRIVATE_KEY }}
        APPID: ${{ secrets.MP_APPID }}
        VERSION: ${{ github.event.inputs.version || '' }}
        DESCRIPTION: ${{ github.event.inputs.description || '自动发布版本' }}
      run: |
        if [[ -z $PRIVATE_KEY ]]; then
          echo "##########"
          echo ""
          echo "Please set 'MP_PRIVATE_KEY' key in actions secrets"
          echo ""
          echo "##########"
          exit 1
        else
          echo "$PRIVATE_KEY" > private.key

          # 如果是手动触发，使用输入的版本号和描述
          if [[ "${{ github.event_name }}" == "workflow_dispatch" && -n "$VERSION" ]]; then
            echo '{
              "weixin": {
                "appid": "'$APPID'",
                "privateKeyPath": "private.key",
                "projectPath": "dist/build/mp-weixin",
                "setting": {
                  "minifyJS": true,
                  "minifyWXML": true,
                  "minifyWXSS": true,
                  "minify": true
                }
              },
              "version": "'$VERSION'",
              "desc": "'$DESCRIPTION'"
            }' > .minicirc
          else
            echo '{
              "weixin": {
                "appid": "'$APPID'",
                "privateKeyPath": "private.key",
                "projectPath": "dist/build/mp-weixin",
                "setting": {
                  "minifyJS": true,
                  "minifyWXML": true,
                  "minifyWXSS": true,
                  "minify": true
                }
              },
              "version": "",
              "desc": ""
            }' > .minicirc
          fi
        fi

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 20

    - uses: pnpm/action-setup@v4
      name: Install pnpm

    - name: upload
      run: |
        npm i uni-mini-ci -g
        pnpm install
        pnpm upload:mp-weixin
