name: Issue Remove Inactive

on:
  issues:
    types: [edited]
  issue_comment:
    types: [created, edited]

jobs:
  issue-remove-inactive:
    runs-on: ubuntu-latest
    steps:
      - name: remove inactive
        if: github.event.issue.state == 'open' && github.actor == github.event.issue.user.login
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'remove-labels'
          issue-number: ${{ github.event.issue.number }}
          labels: 'need reproduce, pending'