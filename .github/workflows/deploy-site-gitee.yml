name: Deploy VitePress site to Gitee Pages

on:
  push:
    branches:
      - release

jobs:
  deploy-and-sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v4


      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm

      - name: Install dependencies
        run: pnpm install

      - name: Build Site
        run: pnpm build:docs

      # 将文档产物提交到gh-pages分支
      - name: Deploy for Gitee 🚀
        uses: JamesIves/github-pages-deploy-action@v4.4.1
        with:
          branch: gh-pages
          folder: docs/.vitepress/dist
          # enable single-commit to reduce the repo size
          single-commit: true
          clean: true

      - name: Sync to Gite<PERSON>
        uses: wearerequired/git-mirror-action@v1.2.0
        env:
          SSH_PRIVATE_KEY: ${{ secrets.GITEE_RSA_PRIVATE_KEY }}
        with:
          # GitHub 仓库地址
          source-repo: **************:Moonofweisheng/wot-design-uni.git
          # Gitee 仓库地址
          destination-repo: *************:wot-design-uni/wot-design-uni.git
  
      - name: Build Gitee Pages
        uses: yanglbme/gitee-pages-action@main
        with:
          # 替换为你的 Gitee 用户名
          gitee-username: wot-design-uni
          # 注意在 Settings->Secrets 配置 GITEE_PASSWORD
          gitee-password: ${{ secrets.GITEE_PASSWORD }}
          # 注意替换为你的 Gitee 仓库，仓库名严格区分大小写，请准确填写，否则会出错
          gitee-repo: wot-design-uni/wot-design-uni
          # 要部署的分支，默认是 master，若是其他分支，则需要指定（指定的分支必须存在）
          branch: gh-pages
