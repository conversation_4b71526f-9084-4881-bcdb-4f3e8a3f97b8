name: 向 Wot UI 提出新功能需求
description: 创建一个 Issue 描述一下你的功能需求。
title: "[新功能需求] 请在此填写标题"
labels: ["feature: need confirm"]
body:
  - type: markdown
    attributes:
      value: |
        在提交功能需求前，请注意：

        - 确认这是一个通用功能，并且无法通过现有的 API 或 Slot 实现。
        - 尝试在 [Issue](https://github.com/Moonofweisheng/wot-design-uni/issues)列表中搜索，并且没有发现同样的需求。
        - 可以先到 [Discussions 讨论区](https://github.com/Moonofweisheng/wot-design-uni/discussions) 发帖，讨论一下需求是否合理。
        - 请确保描述清楚你的需求，以便其他开发者更好地理解你的需求。

  - type: textarea
    id: description
    attributes:
      label: 这个功能解决了什么问题？
      description: 请尽可能详细地说明这个功能的使用场景。
    validations:
      required: true

  - type: textarea
    id: api
    attributes:
      label: 你期望的 API 是什么样子的？
      description: 描述一下这个新功能的 API，并提供一些代码示例。
      placeholder: |
        ```xml
        <wd-interesting some-prop="xxx" />
        ```
    validations:
      required: true