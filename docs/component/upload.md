# Upload 上传

图片、视频和文件上传组件

::: tip 提示
目前组件库已兼容的平台，都支持上传视频，使用`video`组件实现的视频封面在`H5`、`微信小程序`和`支付宝小程序`平台得到支持，而在`钉钉小程序`和`App`平台则受限于`video`标签在这两个平台的能力无法用做视频封面。故推荐在`change`事件中获取视频封面并给`fileList`对应视频添加封面：`thumb`（上传至各种云服务器时，各厂商应该都提供了视频封面的功能）。
:::

::: warning 关于微信小程序隐私协议
`upload`在微信小程序平台使用了`wx.chooseImage`、`wx.chooseMedia`、`wx.chooseVideo`三个隐私接口需要配置微信隐私协议，可以参考[小程序隐私协议开发指南](https://developers.weixin.qq.com/miniprogram/dev/framework/user-privacy/PrivacyAuthorize.html)进行相关配置和开发，否则会导致上传功能无法使用。推荐使用[微信小程序隐私保护弹出框](https://ext.dcloud.net.cn/plugin?id=14346)或者组件库演示用的[微信隐私协议弹框](https://github.com/Moonofweisheng/wot-design-uni/tree/master/src/components/wd-privacy-popup)。
:::

## 基本用法

`file-list` 设置上传列表，数据类型为数组；

数据更改后通过绑定 `change` 事件给 fileList 赋值。

`action` 设置上传的地址；

```html
<wd-upload :file-list="fileList" image-mode="aspectFill" :action="action" @change="handleChange"></wd-upload>
```

```typescript
const fileList = ref<any[]>([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])

const action: string = 'https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload'

function handleChange({ fileList: files }) {
  fileList.value = files
}
```

## 双向绑定 `1.3.8`

`file-list` 支持用 `v-model` 进行双向绑定。

上传、删除等操作会都会同步数据，不需要通过 `change` 事件进行绑定

```html
<wd-upload v-model:file-list="fileList1" image-mode="aspectFill" :action="action"></wd-upload>
```

```typescript
const fileList = ref<any[]>([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])

const action: string = 'https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload'
```

## 禁用

设置 `disabled` 开启禁用上传

```html
<wd-upload
  :file-list="fileList"
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
  disabled
></wd-upload>
```

## 多选上传

通过设置 `multiple` 开启文件多选上传。

```html
<wd-upload
  :file-list="fileList"
  multiple
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
></wd-upload>
```

## 最大上传数限制

上传组件可通过设置 `limit` 来限制上传文件的个数。

```html
<wd-upload
  :file-list="fileList"
  :limit="3"
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
></wd-upload>
```

## 覆盖上传

上传组件可通过设置 `reupload` 来实现在选中时自动替换上一个文件。

```html
<wd-upload
  :file-list="fileList"
  reupload
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
></wd-upload>
```

## 拦截预览图片操作

设置 `before-preview` 函数，在用户点击图片进行预览时，会执行 `before-preview` 函数，接收 { file: 预览文件, index: 当前预览的下标, imgList: 所有图片地址列表, resolve }，通过 `resolve` 函数告知组件是否确定通过，`resolve` 接受 1 个 boolean 值，`resolve(true)` 表示选项通过，`resolve(false)` 表示选项不通过，不通过时不会执行预览图片操作。

```html
<wd-upload
  :file-list="fileList"
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
  :before-preview="beforePreview"
></wd-upload>
```

```typescript
import { useToast, useMessage } from '@/uni_modules/wot-design-uni'

const messageBox = useMessage()
const toast = useToast()
const fileList = ref<any[]>([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])

const beforePreview = ({ file, resolve }) => {
  messageBox
    .confirm({
      msg: '是否预览图片',
      title: '提示'
    })
    .then(() => {
      resolve(true)
    })
    .catch(() => {
      toast.show('取消预览操作')
    })
}

function handleChange({ fileList }) {
  fileList.value = fileList
}
```

## 上传前置处理

设置 `before-upload` 函数，弹出图片选择界面，在用户选择图片点击确认后，会执行 `before-upload` 函数，接收 { files: 当前上传的文件, fileList: 文件列表, resolve }，可以对 `file` 进行处理，并通过 `resolve` 函数告知组件是否确定通过，`resolve` 接受 1 个 boolean 值，`resolve(true)` 表示选项通过，`resolve(false)` 表示选项不通过，不通过时不会执行上传操作。

```html
<wd-upload
  :file-list="fileList"
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
  :before-upload="beforeUpload"
></wd-upload>
```

```typescript
import { useToast, useMessage } from '@/uni_modules/wot-design-uni'

const messageBox = useMessage()
const toast = useToast()
const fileList = ref<any[]>([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])

const beforeUpload = ({ files, resolve }) => {
  messageBox
    .confirm({
      msg: '是否上传',
      title: '提示'
    })
    .then(() => {
      resolve(true)
    })
    .catch(() => {
      toast.show('取消上传操作')
    })
}

function handleChange({ fileList }) {
  fileList.value = fileList
}
```

## 移除图片前置处理

设置 `before-remove` 函数，在用户点击关闭按钮时，会执行 `before-remove` 函数，接收 { file: 移除的文件, index: 移除文件的下标, fileList: 文件列表, resolve }，可以对 `file` 进行处理，并通过 `resolve` 函数告知组件是否确定通过，`resolve` 接受 1 个 boolean 值，`resolve(true)` 表示选项通过，`resolve(false)` 表示选项不通过，不通过时不会执行移除图片操作。

```html
<wd-upload
  :file-list="fileList"
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
  :before-remove="beforeRemove"
></wd-upload>
```

```typescript
import { useToast, useMessage } from '@/uni_modules/wot-design-uni'

const messageBox = useMessage()
const toast = useToast()
const fileList = ref<any[]>([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])

const beforeRemove = ({ file, fileList, resolve }) => {
  messageBox
    .confirm({
      msg: '是否删除',
      title: '提示'
    })
    .then(() => {
      toast.success('删除成功')
      resolve(true)
    })
    .catch(() => {
      toast.show('取消删除操作')
    })
}

function handleChange({ fileList }) {
  fileList.value = fileList
}
```

## 选择文件前置处理

设置 `before-choose` 函数，在用户点击唤起项时，会执行 `before-choose` 函数，接收 { fileList: 文件列表, resolve }，通过 `resolve` 函数告知组件是否确定通过，`resolve` 接受 1 个 boolean 值，`resolve(true)` 表示选项通过，`resolve(false)` 表示选项不通过，不通过时不会执行选择文件操作。

```html
<wd-upload
  :file-list="fileList"
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
  :before-choose="beforeChoose"
></wd-upload>
```

```typescript
import { useToast, useMessage } from '@/uni_modules/wot-design-uni'

const messageBox = useMessage()
const toast = useToast()
const fileList = ref<any[]>([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])

const beforeChoose = ({fileList, resolve}) => {
  messageBox
    .confirm({
      msg: '是否选择',
      title: '提示'
    })
    .then(() => {
      resolve(true)
    })
    .catch(() => {
      toast.show('取消选择操作')
    })
}

function handleChange({ fileList }) {
  fileList.value = fileList
}
```

## 上传至云存储

设置 `buildFormData` 函数，在用户点击上传时，会执行 `buildFormData` 函数，接收 `{ file, formData, resolve }`

- `file` 当前上传的文件
- `formData` 待处理的`formData`
- `resolve` 函数，用于告知组件是否组装`formData`成功，`resolve(formData)` 表示组装成功。

```html
<wd-upload :file-list="files" :action="host" :build-form-data="buildFormData" @change="handleChange"></wd-upload>
```

:::tip 参考

- 上传至阿里云 OSS 的示例，参考[地址](https://help.aliyun.com/zh/oss/use-cases/use-wechat-mini-programs-to-upload-objects)
- 上传至腾讯云 COS 的示例，参考[地址](https://cloud.tencent.com/document/product/436/34929)
- 上传至华为云 OBS 的示例，参考[地址](https://support.huaweicloud.com/bestpractice-obs/obs_05_2000.html)
  :::

::: code-group

```ts [阿里云OSS]
const host = ref<string>('Bucket访问域名，例如https://examplebucket.oss-cn-zhangjiakou.aliyuncs.com')

const files = ref<Record<string, any>[]>([])

function handleChange({ fileList }) {
  files.value = fileList
}

/* *
 * 构建 formData
 * @param {Object} { file, formData, resolve }
 * @return {Object} formData
 * */
const buildFormData = ({ file, formData, resolve }) => {
  let imageName = file.url.substring(file.url.lastIndexOf('/') + 1) // 从图片路径中截取图片名称
  // #ifdef H5
  // h5端url中不包含扩展名，可以拼接一下name
  imageName = imageName + file.name
  // #endif
  const signature = 'your <signatureString>' // 签名信息
  const ossAccessKeyId = 'your <accessKey>' // 你的AccessKey ID
  const policy = 'your <policyBase64Str>' // policy信息
  const key = `20231120/${imageName}` // 图片上传到oss的路径(拼接你的文件夹和文件名)
  const success_action_status = '200' // 将上传成功状态码设置为200，默认状态码为204

  formData = {
    ...formData,
    key: key,
    OSSAccessKeyId: ossAccessKeyId,
    policy: policy,
    signature: signature,
    success_action_status: success_action_status
  }
  resolve(formData) // 组装成功后返回 formData，必须返回
}
```

```TS [腾讯云COS]
const host = ref<string>('Bucket访问域名，例如https://examplebucket.oss-cn-zhangjiakou.aliyuncs.com')

const files = ref<Record<string, any>[]>([])

function handleChange({ fileList }) {
  files.value = fileList
}

/* *
 * 构建 formData
 * @param {Object} { file, formData, resolve }
 * @return {Object} formData
 * */
const buildFormData = ({ file, formData, resolve }) => {
  let imageName = file.url.substring(file.url.lastIndexOf('/') + 1) // 从图片路径中截取图片名称
  // #ifdef H5
  // h5端url中不包含扩展名，可以拼接一下name
  imageName = imageName + file.name
  // #endif
  const policy = 'your policy' // policy信息
  const key = `20231120/${imageName}` // 图片上传到oss的路径(拼接你的文件夹和文件名)
  const qAk = 'your qAk'
  const qSignAlgorithm = 'your qSignAlgorithm'
  const qKeyTime = 'your qKeyTime'
  const qSignature = 'your qSignature'
  const success_action_status = '200' // 将上传成功状态码设置为200
  formData = {
    ...formData,
    key: key,
    policy: policy,
    'q-sign-algorithm': qSignAlgorithm,
    'q-ak': qAk,
    'q-key-time': qKeyTime,
    'q-signature': qSignature,
    success_action_status: success_action_status
  }
  resolve(formData) // 组装成功后返回 formData，必须返回
}
```

```ts [华为云OBS]
const host = ref<string>('Bucket访问域名，例如https://examplebucket.oss-cn-zhangjiakou.aliyuncs.com')

const files = ref<Record<string, any>[]>([])

function handleChange({ fileList }) {
  files.value = fileList
}

/* *
 * 构建 formData
 * @param {Object} { file, formData, resolve }
 * @return {Object} formData
 * */
const buildFormData = ({ file, formData, resolve }) => {
  let imageName = file.url.substring(file.url.lastIndexOf('/') + 1) // 从图片路径中截取图片名称
  // #ifdef H5
  // h5端url中不包含扩展名，可以拼接一下name
  imageName = imageName + file.name
  // #endif
  const signature = 'your <signature>' // 签名信息
  const accessKeyId = 'your <accessKeyId>' // 你的AccessKey ID
  const policy = 'your <policyBase64Str>' // policy信息
  const key = `20231120/${imageName}` // 图片上传到oss的路径(拼接你的文件夹和文件名)
  const success_action_status = '200' // 将上传成功状态码设置为200，默认状态码为204

  formData = {
    ...formData,
    key: key,
    policy: policy,
    AccessKeyId: accessKeyId,
    signature: signature,
    success_action_status: success_action_status
  }
  resolve(formData) // 组装成功后返回 formData，必须返回
}
```

:::

## 自定义唤起上传样式

使用默认插槽可以修改唤起上传的样式。

```html
<wd-upload :file-list="fileList" action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload" @change="handleChange">
    <wd-button>上传</wd-button>
</wd-upload>
```

```typescript
const fileList = ref<any[]>([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])
```

## 上传视频

将`accept`设置为`video`可以用于上传视频类型的文件。

```html
<wd-upload accept="video" multiple :file-list="fileList" :action="action" @change="handleChange"></wd-upload>
```

```typescript
const action = ref<string>('https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload')

const fileList = ref([])

function handleChange({ fileList }) {
  fileList.value = fileList
}
```

## 同时上传视频和图片

将`accept`设置为`media`可以用于同时上传视频和图片。仅微信小程序支持。

```html
<wd-upload accept="media" multiple :file-list="fileList" :action="action" @change="handleChange"></wd-upload>
```

```typescript
const action = ref<string>('https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload')

const fileList = ref([])

function handleChange({ fileList }) {
  fileList.value = fileList
}
```

## 仅上传文件

将`accept`设置为`file`可以用于上传除图片和视频以外类型的文件。仅微信小程序支持。

```html
<wd-upload accept="file" multiple :file-list="fileList" :action="action" @change="handleChange"></wd-upload>
```

```typescript
const action = ref<string>('https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload')

const fileList = ref([])

function handleChange({ fileList }) {
  fileList.value = fileList
}
```

## 上传视频图片和文件

将`accept`设置为`all`可以用于上传视频图片和文件。仅微信小程序和 H5 支持。微信小程序使用`chooseMessageFile`实现，H5 使用`chooseFile`实现。

```html
<wd-upload accept="all" multiple :file-list="fileList" :action="action" @change="handleChange"></wd-upload>
```

```typescript
const action = ref<string>('https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload')

const fileList = ref([])

function handleChange({ fileList }) {
  fileList.value = fileList
}
```

## 手动触发上传

设置 `auto-upload` 为 `false` 后，选择文件后不会自动开始上传。调用 `submit` 方法会把未上传的所有文件进行上传。

```html
<wd-upload
  ref="uploader"
  :auto-upload="false"
  :file-list="fileList"
  action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
  @change="handleChange"
></wd-upload>
<wd-button @click="onUploadClick()">开始上传</wd-button>
```

```typescript
const uploader = ref()

const onUploadClick = () => {
  uploader.value?.submit()
}
```

## 自定义上传方法

使用 `upload-method` 调用自定义的方法来上传文件。

```html
<wd-upload v-model:file-list="fileList" :upload-method="customUpload"></wd-upload>
```

```typescript
import type { UploadMethod, UploadFile } from '@/uni_modules/wot-design-uni/components/wd-upload/types'

const fileList = ref<UploadFile[]>([])
const customUpload: UploadMethod = (file, formData, options) => {
  const uploadTask = uni.uploadFile({
    url: action,
    header: options.header,
    name: options.name,
    fileName: options.name,
    fileType: options.fileType,
    formData,
    filePath: file.url,
    success(res) {
      if (res.statusCode === options.statusCode) {
        // 设置上传成功
        options.onSuccess(res, file, formData)
      } else {
        // 设置上传失败
        options.onError({ ...res, errMsg: res.errMsg || '' }, file, formData)
      }
    },
    fail(err) {
      // 设置上传失败
      options.onError(err, file, formData)
    }
  })
  // 设置当前文件加载的百分比
  uploadTask.onProgressUpdate((res) => {
    options.onProgress(res, file)
  })
}
```


## 自定义预览样式

使用 `preview-cover` 插槽可以自定义覆盖在预览区域上方的内容

```html
<wd-upload v-model:file-list="fileList" accept="image" image-mode="aspectFill" :action="action">
  <template #preview-cover="{ file,index }">
            <!-- 小程序拿不到文件 -->
    <view class="preview-cover">{{ file?.name||`文件${index+1}` }}</view>
  </template>
</wd-upload>
<style>
  .preview-cover {
  margin-top: 10rpx;
  text-align: center;
}
</style>
```

```typescript
const fileList = ref<UploadFile[]>([])
const action: string = 'https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload'
```

## 根据文件拓展名过滤

通过设置 `extension` 可以限制选择文件的格式。以下示例限制只能选择 jpg 和 png 格式的图片:

```html
<wd-upload
  v-model:file-list="fileList"
  :extension="['.jpg', '.png']"
  action="https://mockapi.eolink.com/xxx"
></wd-upload>
```

## Attributes

| 参数                          | 说明                                                                                                                                                                           | 类型                                   | 可选值                                         | 默认值                     | 最低版本         |
| ----------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------------------------------------- | ---------------------------------------------- | -------------------------- | ---------------- |
| file-list / v-model:file-list | 上传的文件列表, 例如: [{ name: 'food.jpg', url: 'https://xxx.cdn.com/xxx.jpg' }]                                                                                               | array                                  | -                                              | []                         | -                |
| action                        | 必选参数，上传的地址                                                                                                                                                           | string                                 | -                                              | -                          | -                |
| header                        | 设置上传的请求头部                                                                                                                                                             | object                                 | -                                              | -                          | -                |
| multiple                      | 是否支持多选文件                                                                                                                                                               | boolean                                | -                                              | -                          | -                |
| disabled                      | 是否禁用                                                                                                                                                                       | boolean                                | -                                              | false                      | -                |
| reupload    | 是否开启覆盖上传，开启后会关闭图片预览   | boolean          | -                                              | false                       | 1.5.0 |
| limit                         | 最大允许上传个数                                                                                                                                                               | number                                 | -                                              | -                          | -                |
| show-limit-num                | 限制上传个数的情况下，是否展示当前上传的个数                                                                                                                                   | boolean                                | -                                              | false                      | -                |
| max-size                      | 文件大小限制，单位为`byte`                                                                                                                                                     | number                                 | -                                              | -                          | -                |
| source-type                   | 选择图片的来源，chooseImage 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/media/image.html#chooseimage)                                                        | array / string                         | -                                              | ['album', 'camera']        | -                |
| size-type                     | 所选的图片的尺寸，chooseImage 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/media/image.html#chooseimage)                                                      | array / string                         | -                                              | ['original', 'compressed'] | -                |
| name                          | 文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile) | string                                 | -                                              | file                       | -                |
| formData                      | HTTP 请求中其他额外的 form data，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile)                                     | object                                 | -                                              | -                          | -                |
| header                        | HTTP 请求 Header，Header 中不能设置 Referer，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile)                         | object                                 | -                                              | -                          | -                |
| on-preview-fail               | 预览失败执行操作                                                                                                                                                               | function({ index, imgList })           | -                                              | -                          | -                |
| before-upload                 | 上传文件之前的钩子，参数为上传的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。                                                                        | function({ files, fileList, resolve }) | -                                              | -                          | -                |
| before-choose                 | 选择图片之前的钩子，参数为文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。                                                                                    | function({ fileList, resolve })        | -                                              | -                          | -                |
| before-remove                 | 删除文件之前的钩子，参数为要删除的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。                                                                      | function({ file, fileList, resolve })  | -                                              | -                          | -                |
| before-preview                | 图片预览前的钩子，参数为预览的图片下标和图片列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。                                                                      | function({file, index, imgList, resolve })  | -                                              | -                          | -                |
| build-form-data               | 构建上传`formData`的钩子，参数为上传的文件、待处理的`formData`，返回值为处理后的`formData`，若返回 false 或者返回 Promise 且被 reject，则停止上传。                            | function({ file, formData, resolve })  | -                                              | -                          | 0.1.61           |
| loading-type                  | [加载中图标类型](/component/loading)                                                                                                                                           | string                                 | -                                              | circular-ring              | -                |
| loading-color                 | [加载中图标颜色](/component/loading)                                                                                                                                           | string                                 | -                                              | #ffffff                    | -                |
| loading-size                  | [加载中图标尺寸](/component/loading)                                                                                                                                           | string                                 | -                                              | 24px                       | -                |
| status-key                    | file 数据结构中，status 对应的 key                                                                                                                                             | string                                 | -                                              | status                     | -                |
| image-mode                    | 预览图片的 mode 属性                                                                                                                                                           | ImageMode                              | -                                              | aspectFit                  | -                |
| accept                        | 接受的文件类型                                                                                                                                                                 | UploadFileType                         | **image** **video** **media** **file** **all** | **image**                  | 1.3.0            |
| compressed                    | 是否压缩视频，当 accept 为 video \| media 时生效                                                                                                                               | boolean                                | -                                              | true                       | 1.3.0            |
| maxDuration                   | 拍摄视频最长拍摄时间，当 accept 为 video \| media 时生效，单位秒                                                                                                               | Number                                 | -                                              | 60                         | 1.3.0            |
| camera                        | 使用前置或者后置相机，当 accept 为 video \| media 时生效                                                                                                                       | UploadCameraType                       | **front**                                      | **back**                   | 1.3.0            |
| successStatus                 | 接口响应的成功状态（statusCode）值                                                                                                                                             | number                                 | -                                              | 200                        | 1.3.4            |
| auto-upload                   | 是否选择文件后自动上传。为 false 时应手动调用 submit() 方法开始上传                                                                                                            | boolean                                | -                                              | true                       | 1.3.8 |
| upload-method                 | 自定义上传方法                                                                                                                                                     | UploadMethod                                | -                                              | -                       | 1.3.8 |
| extension | 根据文件拓展名过滤(H5支持全部类型过滤,微信小程序支持all和file时过滤,其余平台不支持) | string[] | - | - | 1.9.0 |

## accept 的合法值

| name  | 说明                                                                                   | 最低版本 |
| ----- | -------------------------------------------------------------------------------------- | -------- |
| image | 图片，全平台支持，微信支持平台使用`chooseMedia`实现                                              | -        |
| video | 视频，全平台支持，微信支持平台使用`chooseMedia`实现                                          | 1.3.0    |
| media | 图片和视频，仅微信支持，使用`chooseMedia`实现                                          | 1.3.0    |
| file  | 从客户端会话选择图片和视频以外的文件，仅微信支持，使用`chooseMessageFile`实现          | 1.3.0    |
| all   | 全部类型的文件，仅微信和 H5 支持，微信使用`chooseMessageFile`，H5 使用`chooseFile`实现 | 1.3.0    |

## 文件选择数量限制

不同平台的文件选择方法具有不同的最大选择数量限制，这些限制由 uni-app 平台 API 本身决定：

### 微信平台

微信小程序平台具有更丰富的文件选择能力和更高的数量上限：

| 选择方法 | 最大选择数量 | 说明 | 适用文件类型 |
| -------- | ------------ | ---- | ------------ |
| `chooseMedia` | 20 | 选择图片和视频时的最大数量限制 | accept 为 `image`、`video` 或 `media` 时使用 |
| `chooseMessageFile` | 100 | 从客户端会话选择文件时的最大数量限制 | accept 为 `file` 或 `all` 时使用 |

### H5平台

H5 平台支持多种文件选择方式：

| 选择方法 | 最大选择数量 | 说明 | 适用文件类型 |
| -------- | ------------ | ---- | ------------ |
| `chooseImage` | 9 | 选择图片时的最大数量限制 | accept 为 `image` 时使用 |
| `chooseVideo` | 1 | 不支持多选，只能选择单个视频文件 | accept 为 `video` 时使用 |
| `chooseFile` | 100 | 选择文件时的最大数量限制 | accept 为 `all` 时使用 |

::: warning H5平台特别说明
count 值在 H5 平台的表现基于浏览器本身的规范。目前测试结果来看，只能限制单选/多选，并不能限制具体数量。并且，在实际的手机浏览器中很少有能够支持多选的。
:::

### 其他平台

其他平台（如支付宝小程序、钉钉小程序、App等）的文件选择能力相对有限：

| 选择方法 | 最大选择数量 | 说明 | 适用文件类型 |
| -------- | ------------ | ---- | ------------ |
| `chooseImage` | 9 | 选择图片时的最大数量限制 | accept 为 `image` 时使用 |
| `chooseVideo` | 1 | 不支持多选，只能选择单个视频文件 | accept 为 `video` 时使用 |

::: tip 提示
- 当设置的 `limit` 或 `maxCount` 超过上述平台限制时，实际选择数量将以平台限制为准
- 微信小程序平台优先使用 `chooseMedia` 来选择图片和视频，具有更高的选择数量上限
- 视频选择在非微信平台受限于 `chooseVideo` API，仅支持单选
- 平台能力的优先级：微信平台 > H5平台 > 其他平台
:::

## file 数据结构

| 键名     | 类型            | 说明                                                  | 最低版本 |
| -------- | --------------- | ----------------------------------------------------- | -------- |
| uid      | number          | 当前上传文件在列表中的唯一标识                        | -        |
| url      | string          | 上传图片地址                                          | -        |
| action   | string          | 上传的地址                                            | -        |
| percent  | number          | 上传进度                                              | -        |
| size     | number          | 响文件尺寸应码                                        | -        |
| status   | string          | 当前图片上传状态。若自定义了 status-key，应取对应字段 | -        |
| response | string / object | 后端返回的内容，可能是对象，也可能是字符串            | -        |

## Slot

| name    | 说明             | 最低版本 |
| ------- | ---------------- | -------- |
| default | 上传唤起插槽样式 | -        |
| preview-cover | 自定义覆盖在预览区域上方的内容 |   1.3.12   |

## Events

| 事件名称    | 说明                   | 参数                                                                                 | 最低版本 |
| ----------- | ---------------------- | ------------------------------------------------------------------------------------ | -------- |
| success     | 上传成功时触发         | event = { file, fileList,formData } file 为当前选上传的文件，'fileList' 上传图片列表 | -        |
| fail        | 上传失败时触发         | event = { error, file,formData } error 错误信息，file 上传失败的文件                 | -        |
| progress    | 上传中时触发           | event = { response, file } response 上传中响应信息，file 为当前选上传的文件          | -        |
| chooseerror | 选择图片失败时触发     | event = { error } error 选择图片失败的错误信息                                       | -        |
| change      | 上传列表修改时触发     | 选中的值 event = { fileList } 'fileList' 上传图片列表                                | -        |
| remove      | 移除图片时触发         | event = { file } file: 移除的文件信息                                                | -        |
| oversize    | 文件大小超过限制时触发 | event = { file } file: 尺寸超出的文件信息                                            | -        |

## Methods

| 方法名称 | 说明         | 参数 | 最低版本         |
| -------- | ------------ | ---- | ---------------- |
| submit   | 手动开始上传 | -    | 1.3.8 |

## Upload 外部样式类

| 类名                 | 说明                     | 最低版本 |
| -------------------- | ------------------------ | -------- |
| custom-class         | 根节点样式类             | -        |
| custom-evoke-class   | 自定义上传按钮样式类     | -        |
| custom-preview-class | 自定义预览图片列表样式类 | -        |


