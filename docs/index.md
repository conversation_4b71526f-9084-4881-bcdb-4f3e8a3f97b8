---
layout: home

title: Wot UI
titleTemplate: 一个基于Vue3+TS开发的uni-app组件库，提供70+高质量组件，支持暗黑模式、国际化和自定义主题。

hero:
  name: Wot UI
  text: 高颜值、轻量化的uni-app组件库
  tagline: 基于Vue3+TS开发，提供70+高质量组件，支持暗黑模式、国际化和自定义主题。
  image:
    src: /logo.png
    alt: Wot Design
  actions:
    - theme: brand
      text: 起步 🚀
      link: /guide/introduction
    - theme: alt
      text: 常见问题
      link: /guide/common-problems
    - theme: alt
      text: 组件列表
      link: /component/button
    - theme: brand
      text: 🥤一杯咖啡
      link: /reward/reward
    - theme: brand
      text: 咨询服务
      link: /guide/consultation

features:
  - icon: 🎯
    title: 多平台覆盖
    details: 支持 微信小程序、支付宝小程序、钉钉小程序、H5、APP 等平台。
  - icon: 🚀
    title: 70+ 组件
    details: 超过 70 个高质量组件，覆盖移动端主流场景。
  - icon: 💪
    title: TypeScript 支持
    details: 使用 Typescript 构建，提供良好的组件类型系统。
  - icon: 🌍
    title: 支持国际化
    details: 支持国际化，内置 15 种语言包。
  - icon: 📖
    title: 提供丰富的文档和组件示例
    details: 文档和组件示例为开发者提供稳定的后勤保障。
  - icon: 🍭
    title: 支持暗黑模式和主题定制
    details: 可以定制css变量以及组件的样式自定义。

footer: false
---


