<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-06-29 14:38:23
 * @LastEditTime: 2025-06-29 17:05:39
 * @LastEditors: weisheng
 * @Description: 
 * @FilePath: /wot-design-uni/docs/.vitepress/theme/components/HomeStar.vue
 * 记得注释
-->
<script setup lang="ts"></script>

<template>
  <p class="home-star">
    <a href="https://github.com/Moonofweisheng/wot-design-uni">
  <img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/Moonofweisheng/wot-design-uni?logo=github&color=%234d80f0&link=https%3A%2F%2Fgithub.com%2FMoonofweisheng%2Fwot-design-uni&style=flat-square">
 </a>


<!-- <a href="https://github.com/Moonofweisheng/wot-design-uni">
  <img alt="GitHub" src="https://img.shields.io/codecov/c/github/Moonofweisheng/wot-design-uni?style=flat-square">
 </a> -->

<a href="https://www.npmjs.com/package/wot-design-uni">
  <img alt="npm" src="https://img.shields.io/npm/dm/wot-design-uni?logo=npm&link=https%3A%2F%2Fwww.npmjs.com%2Fpackage%2Fwot-design-uni&style=flat-square">
</a>

 <a href="https://www.npmjs.com/package/wot-design-uni">
  <img alt="npm" src="https://img.shields.io/npm/v/wot-design-uni?logo=npm&color=%234d80f0&link=https%3A%2F%2Fwww.npmjs.com%2Fpackage%2Fwot-design-uni&style=flat-square">
</a>
  </p>
</template>

<style>
.home-star{
  display: flex;
  align-items: center;
  padding-top: 24px;
  flex-wrap: wrap;
  gap: 12px;
}
</style>
