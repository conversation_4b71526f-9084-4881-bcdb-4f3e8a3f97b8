<script setup lang="ts">
import { defineComponent, h } from 'vue';
defineProps({
  href: {
    type: String,
    required: true
  }
})

const ExternalLinkIconComponent = defineComponent({
  name: 'ExternalLinkIcon',
  render() {
    const ExternalLinkIcon = {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
          <g fill="none">
            <path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z" />
            <path fill="currentColor" d="M11 6a1 1 0 1 1 0 2H5v11h11v-6a1 1 0 1 1 2 0v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2zm9-3a1 1 0 0 1 1 1v5a1 1 0 1 1-2 0V6.414l-8.293 8.293a1 1 0 0 1-1.414-1.414L17.586 5H15a1 1 0 1 1 0-2Z" />
          </g>
        </svg>
      `
    };

    return h('div', {
      innerHTML: ExternalLinkIcon.template,
    })
  }
})
</script>

<template>
  <el-link :href="href" target="_blank" style="text-decoration: none;font-size: 1.2em;" :underline="false">
    <slot />
    <ExternalLinkIconComponent style="margin-left: 0.25em;"> </ExternalLinkIconComponent>
  </el-link>
</template>