.VPSidebar{
  overflow: hidden !important;
}
.VPSidebar:hover{
  overflow-y: auto !important;
}

// scrollbar
* {
  scrollbar-color: var(--el-scrollbar-bg-color) var(--el-fill-color-light);
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar:horizontal {
  height: 6px
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  transition: all 0.2s ease-in-out;

  &:hover {
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.3);
  }
}


.dark {
  ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);

    &:hover {
      background-color: rgba(255, 255, 255, 0.4);
    }
  }
}