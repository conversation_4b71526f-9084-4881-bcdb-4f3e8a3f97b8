/**
 * Colors
 * -------------------------------------------------------------------------- */

:root {
  --vp-c-brand: #4d80f0;
  /* 20 */
  --vp-c-brand-light: #7199f3;
  /* 30 */
  --vp-c-brand-lighter: #7199f3;
  /* 35 */
  --vp-c-brand-lightest: #8bacf5;
  /* 10 */
  --vp-c-brand-dark: #4573d8;
  /* 20 */
  --vp-c-brand-darker: #3e66c0;
  --vp-c-brand-dimm: rgba(100, 108, 255, 0.08);
}

/**
 * Component: Button
 * -------------------------------------------------------------------------- */

:root {
  --vp-button-brand-border: var(--vp-c-brand-light);
  --vp-button-brand-text: var(--vp-c-white);
  --vp-button-brand-bg: var(--vp-c-brand);
  --vp-button-brand-hover-border: var(--vp-c-brand-light);
  --vp-button-brand-hover-text: var(--vp-c-white);
  --vp-button-brand-hover-bg: var(--vp-c-brand-light);
  --vp-button-brand-active-border: var(--vp-c-brand-light);
  --vp-button-brand-active-text: var(--vp-c-white);
  --vp-button-brand-active-bg: var(--vp-button-brand-bg);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(120deg,
      #4d80f0 30%,
      #bd34fe);

  --vp-home-hero-image-background-image: linear-gradient(-45deg,
      #bd34fe 50%,
      #4d80f0 50%);
  --vp-home-hero-image-filter: blur(40px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(72px);
  }
}

/**
 * Component: Custom Block
 * -------------------------------------------------------------------------- */

:root {
  --vp-custom-block-tip-border: var(--vp-c-brand);
  --vp-custom-block-tip-text: var(--vp-c-brand-darker);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
}

.dark {
  --vp-custom-block-tip-border: var(--vp-c-brand);
  --vp-custom-block-tip-text: var(--vp-c-brand-lightest);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
}

/**
 * Component: Algolia
 * -------------------------------------------------------------------------- */

.DocSearch {
  --docsearch-primary-color: var(--vp-c-brand) !important;
}

/**
 * VitePress: Custom fix
 * -------------------------------------------------------------------------- */

/*
  Use lighter colors for links in dark mode for a11y.
  Also specify some classes twice to have higher specificity
  over scoped class data attribute.
*/
.dark .vp-doc a,
.dark .vp-doc a>code,
.dark .VPNavBarMenuLink.VPNavBarMenuLink:hover,
.dark .VPNavBarMenuLink.VPNavBarMenuLink.active,
.dark .link.link:hover,
.dark .link.link.active,
.dark .edit-link-button.edit-link-button,
.dark .pager-link .title {
  color: var(--vp-c-brand-lighter);
}

.dark .vp-doc a:hover,
.dark .vp-doc a>code:hover {
  color: var(--vp-c-brand-lightest);
  opacity: 1;
}

/* Transition by color instead of opacity */
.dark .vp-doc .custom-block a {
  transition: color 0.25s;
}

.vp-sponsor.aside .vp-sponsor-grid.mini .vp-sponsor-grid-image {
  max-width: 124px;
}



/* side bar */

:root {
  --vp-sidebar-width: 338px;
  --vp-sidebar-bg-color: var(--vp-c-bg);
}


.dark {
  --vp-sidebar-bg-color:var(--vp-c-bg-alt)
}