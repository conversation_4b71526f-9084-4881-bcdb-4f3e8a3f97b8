---
layout: home

title: Wot UI
titleTemplate: A uni-app component library based on Vue3+TS, providing 70+ high-quality components, supporting dark mode, internationalization, and custom themes.

hero:
  name: Wot UI
  text: A Beautiful and Lightweight uni-app Component Library
  tagline: Built with Vue3+TS, providing 70+ high-quality components, supporting dark mode, internationalization, and custom themes.
  image:
    src: /logo.png
    alt: Wot Design
  actions:
    - theme: brand
      text: Get Started 🚀
      link: /en-US/guide/introduction
    - theme: alt
      text: Common Problems
      link: /en-US/guide/common-problems
    - theme: alt
      text: Components
      link: /en-US/component/button
    - theme: brand
      text: 🥤Buy Me a Coffee
      link: /en-US/reward/reward
    - theme: alt
      text: ⭐ Showcase
      link: /en-US/guide/cases

features:
  - icon: 🎯
    title: Multi-Platform Support
    details: Supports WeChat Mini Program, Alipay Mini Program, DingTalk Mini Program, H5, APP, and more platforms.
  - icon: 🚀
    title: 70+ Components
    details: Over 70 high-quality components covering mainstream mobile scenarios.
  - icon: 💪
    title: TypeScript Support
    details: Built with TypeScript, providing a robust component type system.
  - icon: 🌍
    title: Internationalization
    details: Supports internationalization with 15 built-in language packs.
  - icon: 📖
    title: Rich Documentation and Examples
    details: Comprehensive documentation and component examples provide stable support for developers.
  - icon: 🍭
    title: Dark Mode and Theme Customization
    details: Customize CSS variables and component styles for your needs.

footer: false
---