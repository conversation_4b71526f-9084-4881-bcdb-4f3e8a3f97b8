# CLI & Templates

Through this section, you can learn how to use the [create-uni](https://github.com/uni-helper/create-uni) CLI to quickly create a `uni-app` project integrated with `Wot UI`, as well as many quick-start projects that have already integrated `Wot UI`.

## CLI

We recommend using [create-uni](https://github.com/uni-helper/create-uni) to create projects, which supports one-click creation of basic projects integrated with Wot UI. Use the following command:

```bash
pnpm create uni <your-project-name> --ts -m pinia -u wot -e
```

This will complete the following tasks:

- Create a TypeScript project
- Integrate Pinia state management
- Automatically configure WotUI component library
- Add ESLint support

For more information, please refer to [create-uni](https://github.com/uni-helper/create-uni).

## Templates

There are many excellent templates that have chosen Wot UI as their base component library. We've selected 3 templates to introduce here, and you can compare and choose for yourself.

### wot-starter

🍀 [wot-starter](https://github.com/wot-ui/wot-starter) is a `uni-app` quick-start project based on [vitesse-uni-app](https://github.com/uni-helper/vitesse-uni-app) with deep integration of the `Wot UI` component library. It's maintained by the `Wot UI` team, saying goodbye to `HBuilderX` and embracing modern frontend development toolchains. If you're a loyal user of `Wot UI`, this template is perfect for you.

You can also combine it with the CLI section [create-uni](https://github.com/uni-helper/create-uni) to create a project. Open the terminal and use the following command:

```bash
pnpm create uni <project-name> -t wot-starter
```

Open the project folder in VS Code:

```bash
code <project-name>
```

Install dependencies:

```bash
pnpm install
```

Run the project:

```bash
pnpm dev
```

### vitesse-uni-app

[vitesse-uni-app](https://github.com/uni-helper/vitesse-uni-app) is a cross-platform quick-start template powered by `Vite & uni-app`, backed by the `Uni Helper` team, saying goodbye to `HBuilderX` and embracing modern frontend development. Although it doesn't have deep integration with the Wot UI component library, it's an excellent clean template worth recommending.

### unibest

[unibest](https://github.com/unibest-tech/unibest) uses the latest frontend technology stack and doesn't need to rely on `HBuilderX`. It runs through command line, with `VSCode` as the recommended editor (or `webstorm`). It has built-in extensive basic functionality and provides numerous auxiliary features, giving you the `best` experience when writing `uniapp`, with optional `Wot UI` support.

### More Templates

Of course, there are more templates for you to choose from. You can compare and select a template you like from the following list.

If you're developing a `uni-app` template integrated with `Wot UI`, you can send an email to `<EMAIL>` to contact me, and I'll add your template to this list as soon as possible.

| Template | Stars | Description |
|----------|-------|-------------|
| [wot-starter](https://github.com/wot-ui/wot-starter) | ![stars](https://img.shields.io/github/stars/wot-ui/wot-starter) | 🍀 A uni-app quick-start project based on vitesse-uni-app with deep integration of Wot UI component library, maintained by the `Wot UI` team, saying goodbye to `HBuilderX` and embracing modern frontend development toolchains. |
| [vitesse-uni-app](https://github.com/uni-helper/vitesse-uni-app) | ![stars](https://img.shields.io/github/stars/uni-helper/vitesse-uni-app) | A cross-platform quick-start template powered by Vite & uni-app, backed by the Uni Helper team, saying goodbye to HBuilderX and embracing modern frontend development. |
| [unibest](https://github.com/unibest-tech/unibest) | ![stars](https://img.shields.io/github/stars/unibest-tech/unibest) | Uses the latest frontend technology stack, doesn't need to rely on `HBuilderX`, runs through command line, with built-in extensive basic functionality and auxiliary features, giving you the `best` experience when writing `uniapp`. |
| [vite-uniapp-template](https://github.com/viarotel-org/vite-uniapp-template) | ![stars](https://img.shields.io/github/stars/viarotel-org/vite-uniapp-template) | A practical-first uni-app starter template. |
| [uni-plus](https://github.com/DaMaiCoding/uni-plus) | ![stars](https://img.shields.io/github/stars/DaMaiCoding/uni-plus) | A "super super super" useful uniapp development template. |
| [uniez-template](https://github.com/zhe-qi/uniez-template) | ![stars](https://img.shields.io/github/stars/zhe-qi/uniez-template) | A uniapp template that prioritizes "functionality" and "development experience". |
| [snail-uni](https://github.com/hu-snail/snail-uni) | ![stars](https://img.shields.io/github/stars/hu-snail/snail-uni) | A UniApp framework template built specifically for developers. |
