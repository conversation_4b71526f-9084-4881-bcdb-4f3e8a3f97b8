{"name": "wot-design-uni", "version": "1.12.4", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Moonofweisheng/wot-design-uni.git"}, "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "docs:serve": "vitepress serve docs", "dev:docs": "vitepress dev docs --port 5174", "build:docs": "pnpm run build:h5 && vitepress build docs && esno ./scripts/demoCopy.ts", "preinstall": "npx only-allow pnpm", "prepare": "husky install", "lint": "eslint --fix --ext .js,.vue,.ts src", "commit": "git-cz", "release-major": "standard-version --release-as major", "release-minor": "standard-version --release-as minor", "release-patch": "standard-version --release-as patch", "release-tag": "esno ./scripts/release.ts", "build:web-types": "esno ./scripts/build-web-types.ts", "compiler": "esno ./scripts/compiler.ts", "upload:mp-weixin": "uni build -p mp-weixin && minici --platform weixin", "upload:mp-alipay": "uni build -p mp-alipay && minici --platform alipay", "upload:mp-dingtalk": "uni build -p mp-dingtalk && minici --platform dd", "build:qrcode": "esno ./scripts/qrcode.ts", "build:changelog": "esno ./scripts/syncChangelog.ts", "build:theme-vars": "esno ./scripts/buildThemeVars.ts", "test": "vitest", "test:h5": "cross-env UNI_PLATFORM=h5 vitest", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin vitest", "test:all": "npm run test:h5 && npm run test:mp-weixin", "coverage": "vitest run --coverage", "coverage:h5": "cross-env UNI_PLATFORM=h5 vitest run --coverage", "test:component": "esno ./scripts/test-component.ts", "test:workflow": "esno ./scripts/test-workflow.ts"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4050720250324001", "@dcloudio/uni-app-harmony": "3.0.0-4050720250324001", "@dcloudio/uni-app-plus": "3.0.0-4050720250324001", "@dcloudio/uni-components": "3.0.0-4050720250324001", "@dcloudio/uni-h5": "3.0.0-4050720250324001", "@dcloudio/uni-mp-alipay": "3.0.0-4050720250324001", "@dcloudio/uni-mp-baidu": "3.0.0-4050720250324001", "@dcloudio/uni-mp-jd": "3.0.0-4050720250324001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4050720250324001", "@dcloudio/uni-mp-lark": "3.0.0-4050720250324001", "@dcloudio/uni-mp-qq": "3.0.0-4050720250324001", "@dcloudio/uni-mp-toutiao": "3.0.0-4050720250324001", "@dcloudio/uni-mp-weixin": "3.0.0-4050720250324001", "@dcloudio/uni-mp-xhs": "3.0.0-4050720250324001", "@dcloudio/uni-quickapp-webview": "3.0.0-4050720250324001", "element-plus": "^2.3.9", "vite-plugin-compression": "^0.5.1", "vitepress": "^1.6.3", "vue": "^3.4.38", "vue-i18n": "9.1.9"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/preset-typescript": "^7.26.0", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@dcloudio/types": "^3.4.12", "@dcloudio/uni-automator": "3.0.0-4050720250324001", "@dcloudio/uni-cli-shared": "3.0.0-4050720250324001", "@dcloudio/uni-stacktracey": "3.0.0-4050720250324001", "@dcloudio/vite-plugin-uni": "3.0.0-4050720250324001", "@element-plus/icons-vue": "^2.3.1", "@rollup/pluginutils": "^5.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^18.15.3", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@uni-helper/uni-types": "1.0.0-alpha.4", "@uni-helper/vite-plugin-uni-components": "^0.1.0", "@vant/area-data": "^1.4.1", "@vant/touch-emulator": "^1.4.0", "@vitalets/google-translate-api": "^9.2.1", "@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "^1.6.0", "@vue/runtime-core": "^3.4.38", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.1.3", "@vueuse/core": "^12.0.0", "axios": "^1.7.9", "components-helper": "^2.2.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "esno": "^4.8.0", "fast-glob": "^3.3.3", "git-cz": "^4.9.0", "husky": "^8.0.3", "inquirer": "^12.3.2", "jsdom": "^26.0.0", "json5": "^2.2.3", "lint-staged": "^13.2.0", "mini-types": "^0.1.7", "miniprogram-api-typings": "^3.12.3", "prettier": "^2.8.4", "query-string": "^8.1.0", "rimraf": "^4.4.0", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.59.3", "standard-version": "^9.5.0", "typescript": "^5.5.4", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.19", "vitepress-plugin-llms": "^1.1.3", "vitest": "^1.6.0", "vue-eslint-parser": "^9.1.0", "vue-tsc": "^2.0.29"}, "config": {"commitizen": {"path": "git-cz"}}, "standard-version": {"skip": {"tag": true}}, "browserslist": ["Android >= 4.4", "ios >= 9"], "lint-staged": {"*.{js,ts,vue}": "eslint --fix --ext .js,.vue,.ts src"}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}}}, "files": ["lib"], "packageManager": "pnpm@9.2.0"}