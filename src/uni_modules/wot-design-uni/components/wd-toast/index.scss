@import "./../common/abstracts/_mixin.scss";
@import "./../common/abstracts/variable.scss";

@include b(toast) {
  display: inline-block;
  max-width: $-toast-max-width;
  padding: $-toast-padding;
  background-color: $-toast-bg;
  border-radius: $-toast-radius;
  color: $-toast-color;
  transition: all 0.2s;
  font-size: $-toast-fs;
  box-sizing: border-box;
  box-shadow: $-toast-box-shadow;

  @include when(vertical) {
    flex-direction: column;
  }

  @include e(msg) {
    font-size: $-toast-fs;
    word-break: break-all;
    line-height: $-toast-line-height;
    text-align: left;
    font-family: "San Francisco", <PERSON><PERSON><PERSON>, arial, "PingFang SC", "Noto SansCJK", "Microsoft Yahei", sans-serif;
  }
  @include edeep(icon) {
    display: inline-block;
    margin-right: $-toast-icon-margin-right;
    font-size: $-toast-icon-size;

    @include when(vertical) {
      margin-right: 0;
      margin-bottom: $-toast-icon-margin-bottom;
    }
  }
  @include e(iconWrap){
    font-size: 0;
    line-height: 0;
    vertical-align: middle;
  }
  @include e(iconBox){
    display: block;
    width: 100%;
    height: 100%;
  }
  @include e(iconSvg){
    width: $-toast-icon-size;
    height: $-toast-icon-size;
    background-size: cover;
    background-repeat: no-repeat;
  }
  @include e(loading) {
    margin-bottom: $-toast-loading-margin-bottom;
    display: inline-block;
  }
  @include m(top) {
    transform: translate3d(0, -40vh, 0);
  }
  @include m(middle-top){
    transform: translate3d(0%,-18.8vh,0);
  }
  @include m(bottom) {
    transform: translate3d(0, 40vh, 0);
  }
  @include m(with-icon) {
    min-width: $-toast-with-icon-min-width;
    display: inline-flex;
    align-items: center;
  }
  @include m(loading) {
    min-width: auto;
    padding: $-toast-loading-padding;
  }
}
