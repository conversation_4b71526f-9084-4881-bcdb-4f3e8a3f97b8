@import '../common/abstracts/variable';
@import '../common/abstracts/mixin';

.wot-theme-dark {
  @include b(col-picker) {
    @include e(list-item) {
      @include when(disabled) {
        color: $-dark-color3;
      }
    }

    @include e(list-item-tip) {
      color: $-dark-color-gray;
    }

    :deep(.wd-col-picker__arrow) {
      color: $-dark-color;
    }

    @include e(list) {
      color: $-dark-color;
    }

    @include e(selected) {
      color: $-dark-color;
    }

    :deep(.wd-col-picker__cell--placeholder) {
      .wd-cell__value {
        color: $-dark-color-gray;
      }
    }
  }
}

@include b(col-picker) {
  @include edeep(cell) {
    @include when(disabled) {
      .wd-cell__value {
        color: $-input-disabled-color;
        cursor: not-allowed;
      }
    }
    @include when(error) {
      .wd-cell__value {
        color: $-input-error-color;
      }
      .wd-col-picker__arrow {
        color: $-input-error-color;
      }
    }
    @include when(large) {
      .wd-col-picker__arrow {
        font-size: $-cell-icon-size-large;
      }
    }

    @include m(placeholder) {
      .wd-cell__value {
        color: $-input-placeholder-color;
      }
    }
  
  }
  
  @include edeep(arrow) {
    display: block;
    font-size: $-cell-icon-size;
    color: $-cell-arrow-color;
    line-height: $-cell-line-height;
  }

  @include e(selected) {
    height: $-col-picker-selected-height;
    font-size: $-col-picker-selected-fs;
    color: $-col-picker-selected-color;
    overflow: hidden;
  }
  
  @include e(selected-container){
    position: relative;
    display: flex;
    user-select: none;
  }
  
  @include e(selected-item) {
    flex: 0 0 auto;
    height: $-col-picker-selected-height;
    line-height: $-col-picker-selected-height;
    padding: $-col-picker-selected-padding;

    @include when(selected) {
      font-weight: $-col-picker-selected-fw;
    }
  }
  
  @include e(selected-line) {
    position: absolute;
    bottom: 5px;
    width: $-col-picker-line-width;
    left: 0;
    height: $-col-picker-line-height;
    background: $-col-picker-line-color;
    z-index: 1;
    border-radius: calc($-col-picker-line-height / 2);
    box-shadow: $-col-picker-line-box-shadow;
  }
  
  @include e(list-container){
    position: relative;
  }
  
  @include e(list) {
    height: $-col-picker-list-height;
    padding-bottom: $-col-picker-list-padding-bottom;
    box-sizing: border-box;
    overflow: auto;
    color: $-col-picker-list-color;
    font-size: $-col-picker-list-fs;
    -webkit-overflow-scrolling: touch;
  }
  
  @include e(list-item) {
    display: flex;
    padding: $-col-picker-list-item-padding;
    align-items: flex-start;

    @include when(selected) {
      color: $-col-picker-list-color-checked;

      :deep(.wd-col-picker__checked) {
        opacity: 1;
      }
    }
    @include when(disabled) {
      color: $-col-picker-list-color-disabled;
    }
  }
  
  @include e(list-item-label) {
    line-height: 1.285;
  }
  
  @include e(list-item-tip) {
    margin-top: 2px;
    font-size: $-col-picker-list-fs-tip;
    color: $-col-picker-list-color-tip;
  }
  
  @include edeep(checked) {
    display: block;
    margin-left: 4px;
    font-size: $-col-picker-list-checked-icon-size;
    color: $-col-picker-list-color-checked;
    opacity: 0;
  }
  
  @include e(loading) {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    align-items: center;
    justify-content: center;
  }
}