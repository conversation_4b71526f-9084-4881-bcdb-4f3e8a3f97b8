@import "./../common/abstracts/_mixin.scss";
@import "./../common/abstracts/variable.scss";

.wot-theme-dark {
  @include b(progress) {
    @include e(label) {
      color: $-dark-color3;
    }
  }
}

@include b(progress) {
  width: 100%;
  height: $-progress-height;
  display: flex;
  align-items: center;
  padding: $-progress-padding;

  @include e(outer) {
    display: block;
    position: relative;
    flex: 1;
    height: $-progress-height;
    border-radius: calc($-progress-height / 2);
    background: $-progress-bg;
  }
  @include e(inner) {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    border-radius: calc($-progress-height / 2);
    background: $-progress-color;
    transition-property: width;
    transition-timing-function: linear;
    font-size: $-progress-icon-fs;

    @include when(danger) {
      background: $-progress-danger-color;
    }
    @include when(success) {
      background: $-progress-success-color;
    }
    @include when(warning) {
      background: $-progress-warning-color;
    }
  }
  @include edeep(label) {
    width: 30px;
    margin-left: 9px;
    color: $-progress-label-color;
    font-size: $-progress-label-fs;
  }
  @include edeep(icon) {
    font-size: $-progress-icon-fs;

    @include when(danger) {
      color: $-progress-danger-color;
    }
    @include when(success) {
      color: $-progress-success-color;
    }
    @include when(warning) {
      color: $-progress-warning-color;
    }
  }
}