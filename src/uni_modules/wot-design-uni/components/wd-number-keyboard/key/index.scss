@import "./../../common/abstracts/_mixin.scss";
@import "./../../common/abstracts/variable.scss";

.wot-theme-dark {
  @include b(key) {
    background: $-dark-background2;
    color: $-dark-color;

    &:active {
      background-color: $-dark-background4;
    }

    @include m(active) {
      background-color: $-dark-background4;
    }

  }
}

.wd-key-wrapper {
  position: relative;
  flex: 1;
  flex-basis: 33%;
  box-sizing: border-box;
  padding: 0 6px 6px 0;

  @include m(wider) {
    flex-basis: 66%;
  }
}

@include b(key) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: $-number-keyboard-key-height;
  font-size: $-number-keyboard-key-font-size;
  line-height: 1.5;
  background: $-number-keyboard-key-background;
  border-radius: $-number-keyboard-key-border-radius;

  &:active {
    background-color: $-number-keyboard-key-active-color;
  }

  @include m(large) {
    position: absolute;
    top: 0;
    right: 6px;
    bottom: 6px;
    left: 0;
    height: auto;
  }

  @include m(delete, close) {
    font-size: $-number-keyboard-delete-font-size;
  }

  @include m(active) {
    background-color: $-number-keyboard-key-active-color;
  }

  @include m(close) {
    color: $-number-keyboard-button-text-color;
    background: $-number-keyboard-button-background;

    &:active {
      background: $-number-keyboard-button-background;
      opacity: $-number-keyboard-button-active-opacity;
    }
  }

  @include edeep(loading-icon) {
    color: $-number-keyboard-button-text-color;
  }

  @include edeep(icon) {
    font-size: $-number-keyboard-icon-size;
  }

}