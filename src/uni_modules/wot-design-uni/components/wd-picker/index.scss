@import "./../common/abstracts/_mixin.scss";
@import "./../common/abstracts/variable.scss";

.wot-theme-dark {
  @include b(picker) {
    @include e(action) {
      @include m(cancel) {
        color: $-dark-color;
      }
      @include when(loading) {
        color: $-dark-color3;
      }
    }

    :deep(.wd-picker__arrow),
    :deep(.wd-picker__clear) {
      color: $-dark-color;
    }

    :deep(.wd-picker__cell--placeholder) {
      .wd-cell__value {
        color: $-dark-color-gray;
      }
    }
  }
}

@include b(picker) {
  @include edeep(popup) {
    border-radius: 16px 16px 0px 0px;
  }

  @include edeep(cell) {
    @include when(disabled) {
      .wd-cell__value {
        color: $-input-disabled-color;
        cursor: not-allowed;
      }
    }
    @include when(error) {
      .wd-cell__value {
        color: $-input-error-color;
      }
      .wd-picker__arrow {
        color: $-input-error-color;
      }
    }
    @include when(large) {
      .wd-picker__arrow,
      .wd-picker__clear {
        font-size: $-cell-icon-size-large;
      }
    }
    @include m(placeholder) {
      .wd-cell__value {
        color: $-input-placeholder-color;
      }
    }
  }
  
  @include edeep(arrow, clear) {
    display: block;
    font-size: $-cell-icon-size;
    color: $-cell-arrow-color;
    line-height: $-cell-line-height;
  }

  @include edeep(clear) {
    color: $-cell-clear-color;
  }

  @include e(wraper) {
    padding-bottom: var(--window-bottom);
  }

  @include e(toolbar) {
    position: relative;
    display: flex;
    font-size: $-picker-toolbar-fs;
    height: $-picker-toolbar-height;
    line-height: $-picker-action-height;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
  }

  @include e(action) {
    display: block;
    border: none;
    outline: none;
    font-size: $-picker-toolbar-fs;
    color: $-picker-toolbar-finish-color;
    background: transparent;
    padding: 24px 15px 14px 15px;

    @include m(cancel) {
      color: $-picker-toolbar-cancel-color;
    }

    @include when(loading) {
      color: $-picker-loading-button-color;
    }
  }
  
  @include e(title) {
    display: block;
    float: 1;
    color: $-picker-toolbar-title-color;
  }
}
