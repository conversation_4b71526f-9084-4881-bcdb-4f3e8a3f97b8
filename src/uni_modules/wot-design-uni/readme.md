<p align="center">
    <img alt="logo" src="https://wot-design-uni.cn/logo.png" width="200">
</p>
<h1 align="center">Wot UI</h1>

<p align="center">📱 一个基于vue3+Typescript构建，参照<a href="https://ftf.jd.com/wot-design/">wot-design</a>打造的uni-app组件库</p>

<p align="center">

<a href="https://github.com/Moonofweisheng/wot-design-uni">
  <img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/Moonofweisheng/wot-design-uni?logo=github&color=%234d80f0&link=https%3A%2F%2Fgithub.com%2FMoonofweisheng%2Fwot-design-uni&style=flat-square">
 </a>


<a href="https://github.com/Moonofweisheng/wot-design-uni">
  <img alt="GitHub" src="https://img.shields.io/codecov/c/github/Moonofweisheng/wot-design-uni?style=flat-square">
 </a>

<a href="https://www.npmjs.com/package/wot-design-uni">
  <img alt="npm" src="https://img.shields.io/npm/dm/wot-design-uni?logo=npm&link=https%3A%2F%2Fwww.npmjs.com%2Fpackage%2Fwot-design-uni&style=flat-square">
</a>

 <a href="https://www.npmjs.com/package/wot-design-uni">
  <img alt="npm" src="https://img.shields.io/npm/v/wot-design-uni?logo=npm&color=%234d80f0&link=https%3A%2F%2Fwww.npmjs.com%2Fpackage%2Fwot-design-uni&style=flat-square">
</a>

<a href="https://github.com/actions-cool/" target="_blank" referrerpolicy="no-referrer">
  <img src="https://img.shields.io/badge/using-actions--cool-red?style=flat-square" alt="actions-cool" />
</a>

<a href="https://app.netlify.com/sites/wot-design-uni/deploys" target="_blank" referrerpolicy="no-referrer">
  <img src="https://api.netlify.com/api/v1/badges/0991d8a9-0fb0-483b-8961-5bde066bbd50/deploy-status" alt="deploy-status" />
</a>

</p>

<p align="center">
  🚀 <a href="https://wot-design-uni.cn">文档网站 (推荐)</a>&nbsp;
  ✈️ <a href="https://wot-design-uni.pages.dev/">文档网站 (cloudflare)</a>&nbsp;
  🔥 <a href="https://wot-design-uni.netlify.app/">文档网站 (Netlify)</a>&nbsp;
  🚫 <a href="https://wot-design-uni.gitee.io/">文档网站 (Gitee暂时下线)</a>
</p>

## ✨ 特性

- 🎯 多平台覆盖，支持 微信小程序、支付宝小程序、钉钉小程序、H5、APP 等.
- 🚀 70+ 个高质量组件，覆盖移动端主流场景.
- 💪 使用 Typescript 构建，提供良好的组件类型系统.
- 🌍 支持国际化，内置 15 种语言包.
- 📖 提供丰富的文档和组件示例.
- 🎨 支持修改 CSS 变量实现主题定制.
- 🍭 支持暗黑模式

## 📱 预览

扫描二维码访问演示，注意：因微信审核机制限制，当前的微信小程序示例可能不是最新版本，可以clone代码到本地预览。

<p style="display:flex;gap:24px">
<img src="https://wot-design-uni.cn/wx.jpg" width="200" height="200"/>
<img src="https://wot-design-uni.cn/alipay.png" width="200" height="200" />
<img src="https://wot-design-uni.cn/h5.png" width="200" height="200" />
<img src="https://wot-design-uni.cn/android.png" width="200" height="200" />

</p>

## 快速上手

详细说明见 [快速上手](https://wot-design-uni.cn/guide/quick-use.html)。

## 链接

- [常见问题](https://wot-design-uni.cn/guide/common-problems.html)
- [更新日志](https://wot-design-uni.cn/guide/changelog.html)
- [Discussions 讨论区](https://github.com/Moonofweisheng/wot-design-uni/discussions)
- [QQ 群](https://wot-design-uni.cn/guide/join-group.html)

## 优秀案例

[这里](https://wot-design-uni.cn/guide/cases.html)我们收集了一些优秀的案例，欢迎大家体验！

我们也非常欢迎大家一起贡献优秀的 Demo 与案例，欢迎在此 [issue](https://github.com/Moonofweisheng/wot-design-uni/issues/16) 提交案例。


## 周边生态

| 项目                                                                                                        | 描述                                                 |
| ----------------------------------------------------------------------------------------------------------- | ---------------------------------------------------- |
| [awesome-uni-app](https://github.com/uni-helper/awesome-uni-app)                                            | 多端统一开发框架 uni-app 优秀开发资源汇总            |
| [create-uni](https://github.com/uni-helper/create-uni)                                                      | 快速创建 uni-app 项目                                |
| [wot-starter](https://github.com/wot-ui/wot-starter)                  | 基于 [vitesse-uni-app](https://github.com/uni-helper/vitesse-uni-app) 的 wot-ui 快速起手项目     |
| [wot-starter-retail](https://github.com/Moonofweisheng/wot-starter-retail)                                  | 基于 wot-design-uni 的 uni-app 零售行业模板          |
| [Wot UI Snippets](https://marketplace.visualstudio.com/items?itemName=kiko.wot-design-uni-snippets) | Wot UI 代码块提示                            |
| [uni-mini-ci](https://github.com/Moonofweisheng/uni-mini-ci)                                                | 一个 uni-app 小程序端构建后支持 CI（持续集成）的插件 |
| [uni-mini-router](https://github.com/Moonofweisheng/uni-mini-router)                                        | 一个基于 vue3 和 Typescript 的轻量级 uni-app 路由库  |
| [unibest](https://github.com/unibest-tech/unibest)                                                              | 基于 wot-design-uni 的 uni-app 模板                  |
| [wot-design-uni AI 助手](https://www.coze.cn/store/bot/7347916532258701363)                                 | 一个能回答你关于 wot-design-uni 组件库问题的智能助手 |
| [uni-ku-root](https://github.com/uni-ku/root)                                                               | 一个模拟 App.vue 原有能力的根组件插件                  |


## 贡献指南

修改代码请阅读我们的 [贡献指南](https://github.com/Moonofweisheng/wot-design-uni/blob/develop/.github/CONTRIBUTING.md)。

使用过程中发现任何问题都可以提 [Issue](https://github.com/Moonofweisheng/wot-design-uni/issues) 给我们，当然，我们也非常欢迎你给我们发 [PR](https://github.com/Moonofweisheng/wot-design-uni/pulls)。

## 贡献者们
感谢以下所有给 Wot UI 贡献过代码的 [开发者](https://github.com/Moonofweisheng/wot-design-uni/graphs/contributors)。


<a href="https://github.com/Moonofweisheng/wot-design-uni/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=Moonofweisheng/wot-design-uni" />
</a>


## 捐赠本项目

开发一个 UI 组件库是一项耗时的工作，尤其是要多端适配。为此 Wot UI 经常肝到深夜 ……  

如果您认为 Wot UI 帮助到了您的开发工作，您可以捐赠 Wot UI 的研发工作，捐赠无门槛，哪怕是一杯可乐也好。

捐赠后您的昵称、留言等将会展示在[捐赠榜单](https://wot-design-uni.cn/reward/donor.html)中。


### 爱发电捐赠

<a href="https://afdian.com/a/weisheng233">https://afdian.com/a/weisheng233</a>

### 扫码捐赠

<p>
<img src="https://wot-design-uni.cn/weixinQrcode.jpg" width="200" height="200" style="margin-right:30px"/>
<img src="https://wot-design-uni.cn/alipayQrcode.jpg" width="200" height="200" />
</p>


## 鸣谢

- [wot-design](https://github.com/jd-ftf/wot-design-mini) - 感谢 wot-design 团队多年来的不断维护，让 wot-design-uni 能够站在巨人的肩膀上。
- [uni-helper](https://github.com/uni-helper) - 感谢 uni-helper 团队提供的 uni-app 工具库，让 wot-design-uni 能够更方便地使用。
- [捐赠者](https://wot-design-uni.cn/reward/donor.html) - 感谢所有捐赠者，是你们的捐赠让 wot-design-uni 能够更好地发展。


## 开源协议

本项目基于 [MIT](https://zh.wikipedia.org/wiki/MIT%E8%A8%B1%E5%8F%AF%E8%AD%89) 协议，请自由地享受和参与开源。