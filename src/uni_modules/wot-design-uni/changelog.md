# 更新日志 


### [1.12.4](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.12.3...v1.12.4) (2025-08-28)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Upload 设置 multiple 无效的问题 ([#1250](https://github.com/Moonofweisheng/wot-design-uni/issues/1250)) ([784aab4](https://github.com/Moonofweisheng/wot-design-uni/commit/784aab4aa156e2a440db7d1a0a10e36358851763))

### [1.12.3](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.12.2...v1.12.3) (2025-08-27)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Fab、Cell组件 Icon 垂直居中异常的问题 ([#1249](https://github.com/Moonofweisheng/wot-design-uni/issues/1249)) ([d7dac97](https://github.com/Moonofweisheng/wot-design-uni/commit/d7dac979982513c88363dd6224352ab099b7d0c3))

### [1.12.2](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.12.1...v1.12.2) (2025-08-26)


### ✨ Features | 新功能

* ✨ 调整 composables 的导出，新增 useCountDown 的导出 ([#1242](https://github.com/Moonofweisheng/wot-design-uni/issues/1242)) ([93fafa1](https://github.com/Moonofweisheng/wot-design-uni/commit/93fafa180fcfb3fa07deab0393449e822179d43b)), closes [#1239](https://github.com/Moonofweisheng/wot-design-uni/issues/1239)
* ✨ DroMenu 图标大小和位置样式逻辑调整为不缩放且不使用绝对定位 ([#1243](https://github.com/Moonofweisheng/wot-design-uni/issues/1243)) ([e59fd56](https://github.com/Moonofweisheng/wot-design-uni/commit/e59fd568be9284b7eb91575a3f59194cd681828c))

### [1.12.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.12.0...v1.12.1) (2025-08-25)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 DropDown 图标垂直居中异常的问题 ([37042c7](https://github.com/Moonofweisheng/wot-design-uni/commit/37042c7766bfe366828f51201c6023aeb4cfaec6))

## [1.12.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.11.1...v1.12.0) (2025-08-24)


### ✨ Features | 新功能

* ✨ 为 Cell、Input、Textarea 等组件添加 marker-side 属性控制必填*位置 ([#1181](https://github.com/Moonofweisheng/wot-design-uni/issues/1181)) ([cade06f](https://github.com/Moonofweisheng/wot-design-uni/commit/cade06fe4a776e55a862a58f1eddc1fe0f055a88)), closes [#555](https://github.com/Moonofweisheng/wot-design-uni/issues/555)
* ✨ 新增维语本地化支持 ([b4e8605](https://github.com/Moonofweisheng/wot-design-uni/commit/b4e86053b4bb73ef89ae91330d50b6e68349ad20))
* ✨ Upload 上传组件调信小程序端使用chooseMedia替换掉chooseImage等 ([57d89f3](https://github.com/Moonofweisheng/wot-design-uni/commit/57d89f33d769362c198048d37b44f7d67ed384f4)), closes [#1193](https://github.com/Moonofweisheng/wot-design-uni/issues/1193)


### 🐛 Bug Fixes | Bug 修复

* 🐛 签字版组件设置exportScale < 1时在支付宝上导出图片不完整 ([#1129](https://github.com/Moonofweisheng/wot-design-uni/issues/1129)) ([555ae5f](https://github.com/Moonofweisheng/wot-design-uni/commit/555ae5f40049ffd958ec4e58e80ccd3ba4f524f3))
* 🐛 修复 grid-item 组件 custom-text 失效的问题 ([#1180](https://github.com/Moonofweisheng/wot-design-uni/issues/1180)) ([5abc3b5](https://github.com/Moonofweisheng/wot-design-uni/commit/5abc3b5344bd2f880ef94350e10c2ef9e9f9250f)), closes [#995](https://github.com/Moonofweisheng/wot-design-uni/issues/995)
* 🐛 修复 signature 组件设置background-color为透明色导致撤销无效 ([#1224](https://github.com/Moonofweisheng/wot-design-uni/issues/1224)) ([2e67adf](https://github.com/Moonofweisheng/wot-design-uni/commit/2e67adf2bae212a03f3cef3113326f4d942b0b55)), closes [#1223](https://github.com/Moonofweisheng/wot-design-uni/issues/1223)
* 🐛 修复 Upload 多选时配置为覆盖上传图片达到上限无法替换图片的问题 ([00ab76b](https://github.com/Moonofweisheng/wot-design-uni/commit/00ab76bd91e91a3d0fabdb8b80e00df764055519))


### ✏️ Documentation | 文档

* ✏️ 修复基础组件文档问题 - 阶段一完成 ([#1220](https://github.com/Moonofweisheng/wot-design-uni/issues/1220)) ([9132174](https://github.com/Moonofweisheng/wot-design-uni/commit/9132174b11c539d2ec5f8d0aec2044ac1affaaff))
* ✏️ 修正 drop-menu-item options 默认数据结构注释 ([#1087](https://github.com/Moonofweisheng/wot-design-uni/issues/1087)) ([7a2cd2d](https://github.com/Moonofweisheng/wot-design-uni/commit/7a2cd2da93746761b05e068083b171312c4af0e3))
* 修复 Button/Icon/ConfigProvider/Popup 组件文档不一致问题 ([#1190](https://github.com/Moonofweisheng/wot-design-uni/issues/1190)) ([04d0d11](https://github.com/Moonofweisheng/wot-design-uni/commit/04d0d1184957bb1ae5b1ee2ddfaa978a989427b3))

### [1.11.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.11.0...v1.11.1) (2025-07-25)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 RootPortal 及 Picker 相关组件 type-check 错误的问题 ([1859294](https://github.com/Moonofweisheng/wot-design-uni/commit/185929461c56d596b6a9d0dabe117e229ceda601))

## [1.11.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.10.0...v1.11.0) (2025-07-20)


### ✨ Features | 新功能

* ✨ 结合Cell组件重构Picker相关组件的触发器 ([4a50f2d](https://github.com/Moonofweisheng/wot-design-uni/commit/4a50f2db5956134d579b3be5830f786deb272364))
* ✨ 新增 root-portal 组件支持从页面中脱离出来，用于解决各种 fixed 失效问题 ([#1155](https://github.com/Moonofweisheng/wot-design-uni/issues/1155)) ([372735a](https://github.com/Moonofweisheng/wot-design-uni/commit/372735a16af7d9a9f3a1a75a8493c2293f2aa216))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 DatetimePicker 超出隐藏 ellipsis 无效的问题 ([cdf1f78](https://github.com/Moonofweisheng/wot-design-uni/commit/cdf1f78513e6116a5f837237af766d00e1244668))
* 🐛 修复 DatetimePicker 区间选择时无法选择绑定值范围以外的时间的问题 ([3c1023c](https://github.com/Moonofweisheng/wot-design-uni/commit/3c1023c9f6e07b17f684f7a62c5c8ffc462b9808)), closes [#1170](https://github.com/Moonofweisheng/wot-design-uni/issues/1170)
* 🐛 修复 Overlay 组件锁定背景滚动属性 lock-scroll 无法取消的问题 ([#1149](https://github.com/Moonofweisheng/wot-design-uni/issues/1149)) ([f3ccf3d](https://github.com/Moonofweisheng/wot-design-uni/commit/f3ccf3d936d7224468a29f869c326d98ea8c87f8))


### ✏️ Documentation | 文档

* ✏️  更新微信小程序演示二维码 ([28d5970](https://github.com/Moonofweisheng/wot-design-uni/commit/28d5970a4f6a9548469fcdc2be48737c13d4014c))
* ✏️  添加脚手架与模板的介绍 ([27243c1](https://github.com/Moonofweisheng/wot-design-uni/commit/27243c134114a7c5c52395f22c07a33760427a33))
* ✏️  添加快速上手项目链接 ([ed923ec](https://github.com/Moonofweisheng/wot-design-uni/commit/ed923ecf99728813410164fe4963c520592a12a4))
* ✏️  文档侧边栏新增版本号显示 ([6c6d5c9](https://github.com/Moonofweisheng/wot-design-uni/commit/6c6d5c9ea3a3016bd4eb9e85e0a6ae1472c02b13))
* ✏️  新增咨询服务章节 ([66f796e](https://github.com/Moonofweisheng/wot-design-uni/commit/66f796e4406309c0f5ad09d1b521aab6962047c9))
* ✏️  修复快速上手页面存在死链的问题 ([722103c](https://github.com/Moonofweisheng/wot-design-uni/commit/722103c46dcfec530c7675f5fac4d6b4e8d6c4ea))
* ✏️ 修复文档中 calendar 类型名 `monthrang` → `monthrange` 的拼写错误。 ([41a6fe3](https://github.com/Moonofweisheng/wot-design-uni/commit/41a6fe3647c9837e0a6cb964882e9e5812ed7b7f))
* **backtop:** ✏️ 修正文档中 BackTop 组件的 bottom 属性描述从「距离屏幕顶部的距离」修改为「距离屏幕底部的距离」 ([5a458fc](https://github.com/Moonofweisheng/wot-design-uni/commit/5a458fc723fe0745dc7de8ffc7e50b5c7f75654d))
* **backtop:** ✏️ 修正文档中 bottom 属性的描述 ([b0cd321](https://github.com/Moonofweisheng/wot-design-uni/commit/b0cd321c5ed4e8f6042bd85551b7efe96e891156))
* **component:** ✏️  移除多余的 H5 平台标题 ([f851d13](https://github.com/Moonofweisheng/wot-design-uni/commit/f851d139b1b00967fc5f2bcb1d7517a58f40adf7))
* **datetime-picker:** 添加 before-confirm 回调中使用loading的提示 ([c61b33f](https://github.com/Moonofweisheng/wot-design-uni/commit/c61b33f51a3a8e00900ba765b72d6056b1299c0c))
* unibest 链接地址更新 ([645e022](https://github.com/Moonofweisheng/wot-design-uni/commit/645e022094122ef3afee81c5f8059429e950a68a))

## [1.10.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.9.1...v1.10.0) (2025-06-29)


### ✏️ Documentation | 文档

* ✏️  调整微信小程序演示demo激励按钮的文案以应对微信审核 ([06323a9](https://github.com/Moonofweisheng/wot-design-uni/commit/06323a9b3228299fa9cf4ff6cd826514ced1dc32))
* ✏️  简化组件库通用名称为 WotUI ([8177978](https://github.com/Moonofweisheng/wot-design-uni/commit/817797801de1228d39ff399c7792d76c2652e94d))
* ✏️  添加 WotUI 加群凭证相关介绍 ([341b9e1](https://github.com/Moonofweisheng/wot-design-uni/commit/341b9e1c1e98699510bbdc4587782b3647d2c4ba))
* ✏️  修正示例 demo 源码链接地址 ([1ccdb4a](https://github.com/Moonofweisheng/wot-design-uni/commit/1ccdb4a7468ce7984ac4f17305212aebc54f62c3)), closes [#1045](https://github.com/Moonofweisheng/wot-design-uni/issues/1045)
* ✏️  优化加群文档使其更加清晰明确 ([207dfb8](https://github.com/Moonofweisheng/wot-design-uni/commit/207dfb8c9bbf4434ec51e186886ad8dba6289153))
* ✏️  优秀案例改为取线上资源 ([053485b](https://github.com/Moonofweisheng/wot-design-uni/commit/053485b470419b5a451b73aec0dc03d12b7dde56))
* ✏️  update cases ([f1ad2fa](https://github.com/Moonofweisheng/wot-design-uni/commit/f1ad2fa05efba00d89958adba5ca967eead01cfe))


### ✨ Features | 新功能

* ✨ DatetimePicker 支持time和date-time类型下配置选择到秒 ([#1117](https://github.com/Moonofweisheng/wot-design-uni/issues/1117)) ([f2e8fda](https://github.com/Moonofweisheng/wot-design-uni/commit/f2e8fdad8026b4b8ce5d11a2e80d5a565031cdc5)), closes [#844](https://github.com/Moonofweisheng/wot-design-uni/issues/844)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 DropDownItem 自定义样式类 CustomIcon 无效的问题 ([cb408f5](https://github.com/Moonofweisheng/wot-design-uni/commit/cb408f553a2e5d84225b5dcdb447dd784992aa6b)), closes [#1119](https://github.com/Moonofweisheng/wot-design-uni/issues/1119)
* 🐛 修复 DropMenu 设置 Modal 无效的问题 ([#1125](https://github.com/Moonofweisheng/wot-design-uni/issues/1125)) ([eddbd5d](https://github.com/Moonofweisheng/wot-design-uni/commit/eddbd5d22e6cb413e5bf06a642f60f6e081a9074)), closes [#1121](https://github.com/Moonofweisheng/wot-design-uni/issues/1121)
* 🐛 修复 Input、Textarea、Search 组件设置清空后不聚焦时无法触发失焦事件的问题 ([#1046](https://github.com/Moonofweisheng/wot-design-uni/issues/1046)) ([33b5565](https://github.com/Moonofweisheng/wot-design-uni/commit/33b556546a03e240cfcb3662286cc6dc70b70263))
* 🐛 修复 Slider 处理边界值异常的问题，优化样式和事件处理逻辑 ([#1050](https://github.com/Moonofweisheng/wot-design-uni/issues/1050)) ([0d7ed81](https://github.com/Moonofweisheng/wot-design-uni/commit/0d7ed8129c623c0b423fe8da6814417417ea3114)), closes [#1023](https://github.com/Moonofweisheng/wot-design-uni/issues/1023)
* 🐛 修复 Toast 英文断行样式错误问题 ([#1067](https://github.com/Moonofweisheng/wot-design-uni/issues/1067)) ([09dde62](https://github.com/Moonofweisheng/wot-design-uni/commit/09dde6278ed9bb0a57709c15ea4dff8da6e786cd))
* 🐛 修复 wd-img-cropper 组件在微信小程序中旋转图片后操作卡顿的问题 ([#1112](https://github.com/Moonofweisheng/wot-design-uni/issues/1112)) ([50782a1](https://github.com/Moonofweisheng/wot-design-uni/commit/50782a16717d483f4dc7095e2f6096591999df01))
* 🐛 修复 wd-slider 滑块不跟手的问题 ([#1114](https://github.com/Moonofweisheng/wot-design-uni/issues/1114)) ([e3dc36e](https://github.com/Moonofweisheng/wot-design-uni/commit/e3dc36e832a74029348a859adb53f06054e9eea6))
* 🐛 修复NumberKeyboard组件使用 title 插槽未传入关闭文本时不展示头部的问题 ([#1060](https://github.com/Moonofweisheng/wot-design-uni/issues/1060)) ([875e072](https://github.com/Moonofweisheng/wot-design-uni/commit/875e072b3ea8dff270cb2214f94b95e03fa210db)), closes [#760](https://github.com/Moonofweisheng/wot-design-uni/issues/760)
* 🐛 优化 InputNumbe 处理中间状态值的逻辑，支持配置不立即响应输入变化 ([#1116](https://github.com/Moonofweisheng/wot-design-uni/issues/1116)) ([ff99b22](https://github.com/Moonofweisheng/wot-design-uni/commit/ff99b22a6930ea500539f403f23d46cd836e8bca))
* 修复 wd-upload 组件的 formData 属性的 ts 类型为 void 的问题 ([#1106](https://github.com/Moonofweisheng/wot-design-uni/issues/1106)) ([6026137](https://github.com/Moonofweisheng/wot-design-uni/commit/60261374acb99f64298c724e1f8d1df3061bd85a))

### [1.9.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.9.0...v1.9.1) (2025-05-08)


### ✏️ Documentation | 文档

* ✏️  处理小程序超包 ([08e9ee1](https://github.com/Moonofweisheng/wot-design-uni/commit/08e9ee1cb366e4c5ec87d989795d58399cb1df32))
* ✏️  修复部分组件二维码显示异常的问题 ([eea9bf7](https://github.com/Moonofweisheng/wot-design-uni/commit/eea9bf7aac36cfd9b7b476392c35e3db097e4891))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Upload 开启 multiple 后只能成功上传最后一个文件的问题 ([#1044](https://github.com/Moonofweisheng/wot-design-uni/issues/1044)) ([f2ae0d3](https://github.com/Moonofweisheng/wot-design-uni/commit/f2ae0d3aacab3b95b48f623623c5bf1f9acecf18)), closes [#1043](https://github.com/Moonofweisheng/wot-design-uni/issues/1043)

## [1.9.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.8.0...v1.9.0) (2025-05-07)


### ⚡ Performance Improvements | 性能优化

* ⚡ 增强 wd-drop-menu 组件，优化遮罩层闪烁 ([#974](https://github.com/Moonofweisheng/wot-design-uni/issues/974)) ([f9605b7](https://github.com/Moonofweisheng/wot-design-uni/commit/f9605b71b667cfaad56b7b028d53ccff5112ddf8))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Curtain 幕帘组件 close 事件触发2次的问题 ([460a838](https://github.com/Moonofweisheng/wot-design-uni/commit/460a83802bad5b38d6e7063e481e510ed69dffb9)), closes [#894](https://github.com/Moonofweisheng/wot-design-uni/issues/894)
* 🐛 修复 message-box 配合 layouts 使用全局 message-box 时，切换页面可能会显示上一次的 message-box 内容的问题 ([f137a63](https://github.com/Moonofweisheng/wot-design-uni/commit/f137a6313e45e65dd23a03ae88b304e07b1a377e))
* 🐛 修复 Textarea 设置为 null 时，显示字数限制显示错误问题 ([#1004](https://github.com/Moonofweisheng/wot-design-uni/issues/1004)) ([60f2fe6](https://github.com/Moonofweisheng/wot-design-uni/commit/60f2fe61ae77e170e2c05e1b20d256ab107ee4db)), closes [#1003](https://github.com/Moonofweisheng/wot-design-uni/issues/1003)
* 🐛 修复部分国际化文本不正确的问题 ([#970](https://github.com/Moonofweisheng/wot-design-uni/issues/970)) ([4319fa9](https://github.com/Moonofweisheng/wot-design-uni/commit/4319fa9badf3e3dd08218e8e90604c91c49b49c7))
* 🐛 修复多个 ImgCropper 存在时生成图片异常的问题 ([1876fc3](https://github.com/Moonofweisheng/wot-design-uni/commit/1876fc3d0c21c2d9d9c45fe3096690a0bde46493))


### ✨ Features | 新功能

* ✨ 将 Tabbar 和 Badge 设置为标准盒子模型 ([#1036](https://github.com/Moonofweisheng/wot-design-uni/issues/1036)) ([7f2ccb2](https://github.com/Moonofweisheng/wot-design-uni/commit/7f2ccb2d372c412670bfe4ac833f8dc957d994e9))
* ✨ 提供 useUpload hooks 用于便捷上传 ([#969](https://github.com/Moonofweisheng/wot-design-uni/issues/969)) ([49fe25a](https://github.com/Moonofweisheng/wot-design-uni/commit/49fe25a99bb0c60b3a81a11ef7eb06e762fb1d25))
* ✨ 添加组合式API文档并提供相关API ([#972](https://github.com/Moonofweisheng/wot-design-uni/issues/972)) ([b852376](https://github.com/Moonofweisheng/wot-design-uni/commit/b85237643ce217c14a304e8477487acce327ed49))
* ✨ 添加input props.type可选类型 ([#983](https://github.com/Moonofweisheng/wot-design-uni/issues/983)) ([91776bf](https://github.com/Moonofweisheng/wot-design-uni/commit/91776bf3de9bdd6c6954ecab8024dddbdcee7e48)), closes [#981](https://github.com/Moonofweisheng/wot-design-uni/issues/981)
* ✨ 文档示例项目支持国际化切换 ([#992](https://github.com/Moonofweisheng/wot-design-uni/issues/992)) ([4ae9587](https://github.com/Moonofweisheng/wot-design-uni/commit/4ae9587d635c1ba80b8855d3b3d5a38939b1825c))
* ✨ 移除 keyboard 虚拟键盘车牌号模式下不可用的 I、O 键 ([#1037](https://github.com/Moonofweisheng/wot-design-uni/issues/1037)) ([90c53d7](https://github.com/Moonofweisheng/wot-design-uni/commit/90c53d7e2c3208827433093023711c93610df7da)), closes [#1005](https://github.com/Moonofweisheng/wot-design-uni/issues/1005)
* ✨ 引入 vitepress-plugin-llms 优化 AI 理解 ([#1034](https://github.com/Moonofweisheng/wot-design-uni/issues/1034)) ([901c754](https://github.com/Moonofweisheng/wot-design-uni/commit/901c7548fca2bc42c597bb5780769f05e63ede96))
* ✨ 引入vitest做组件测试 ([7e84c5c](https://github.com/Moonofweisheng/wot-design-uni/commit/7e84c5c91f3c351922e976255dc97281c1fe371b))
* ✨ grid-item添加hover-class ([#994](https://github.com/Moonofweisheng/wot-design-uni/issues/994)) ([dfaff53](https://github.com/Moonofweisheng/wot-design-uni/commit/dfaff531cb0a78ec1201f323c5d3326ca967c1e7))
* ✨ ImgCropper 图片剪裁支持设置裁剪框宽高比 ([#973](https://github.com/Moonofweisheng/wot-design-uni/issues/973)) ([5a3f85d](https://github.com/Moonofweisheng/wot-design-uni/commit/5a3f85df6f1e3e54954754b832b0427b67559d58)), closes [#920](https://github.com/Moonofweisheng/wot-design-uni/issues/920)
* ✨ Upload 上传组件支持根据扩展名过滤文件 ([#980](https://github.com/Moonofweisheng/wot-design-uni/issues/980)) ([51adb6b](https://github.com/Moonofweisheng/wot-design-uni/commit/51adb6b8a9911e8d64ef376b19a4aef900b52789)), closes [#796](https://github.com/Moonofweisheng/wot-design-uni/issues/796) [#581](https://github.com/Moonofweisheng/wot-design-uni/issues/581)


### ✏️ Documentation | 文档

* ✏️  文档中添加测试覆盖率徽标 ([4ae514c](https://github.com/Moonofweisheng/wot-design-uni/commit/4ae514c716f1c3b0cb2b05443b995eec4fb82b21))
* ✏️  修复全局 closeOutside 影响 DropDown 异步开关的问题 ([49e5d63](https://github.com/Moonofweisheng/wot-design-uni/commit/49e5d63dd17b48c00648bcab05cb93e743ca43ab))
* ✏️  移除 petercatai 聊天助手 ([5c77483](https://github.com/Moonofweisheng/wot-design-uni/commit/5c77483974189e8b22587407ff9baf484a5779da))
* ✏️  优化 `WdConfigProvider` 文档，解决组件命名风格问题导致失效问题 ([#989](https://github.com/Moonofweisheng/wot-design-uni/issues/989)) ([0f693f9](https://github.com/Moonofweisheng/wot-design-uni/commit/0f693f9784224ee0949d4a7efc66985224bf0d5b))
* ✏️  update cases ([a200337](https://github.com/Moonofweisheng/wot-design-uni/commit/a2003378636a25c253a8596f5afbbbcf6ad3e419))

## [1.8.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.7.1...v1.8.0) (2025-03-23)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 DateTimePicker 区域选择时边界值处理错误的问题 ([230e09f](https://github.com/Moonofweisheng/wot-design-uni/commit/230e09ff2ac25550b0efc2628827b70162041aad))
* 🐛 修复 drop-menu-item 有选项值为空字符串时导致新值错误并触发组件内部警告的问题 ([f63de5b](https://github.com/Moonofweisheng/wot-design-uni/commit/f63de5bd1d3453e844c058a9f185c0a5e56bcf67))
* 🐛 修复在template中使用readonly无法通过vue-ts校验的问题 ([ee5b25f](https://github.com/Moonofweisheng/wot-design-uni/commit/ee5b25fbc36a24cab02576757226f26fb9e27777))
* 🐛 修复input、textarea组件placeholder样式在微信小程序无效的问题 ([#944](https://github.com/Moonofweisheng/wot-design-uni/issues/944)) ([1ac115f](https://github.com/Moonofweisheng/wot-design-uni/commit/1ac115fdac310760e630e8745b438f96c6b88386)), closes [#943](https://github.com/Moonofweisheng/wot-design-uni/issues/943)
* 🐛 修复textarea统计多码元字符长度错误的问题 ([#940](https://github.com/Moonofweisheng/wot-design-uni/issues/940)) ([f9d8523](https://github.com/Moonofweisheng/wot-design-uni/commit/f9d85232c4142f0c957fa3c829dd3321c7ad56e5)), closes [#933](https://github.com/Moonofweisheng/wot-design-uni/issues/933)
* **picker:** clear selected value and options when columns is emptied ([496cb73](https://github.com/Moonofweisheng/wot-design-uni/commit/496cb732b1ab0e69517d629e147b673692631f98)), closes [#935](https://github.com/Moonofweisheng/wot-design-uni/issues/935)


### ✨ Features | 新功能

* ✨  Img 组件添加预览图片属性  ([#945](https://github.com/Moonofweisheng/wot-design-uni/issues/945)) ([34a4878](https://github.com/Moonofweisheng/wot-design-uni/commit/34a48783f37a56fb6ed7a77dd29ee5a406bf989a))
* ✨ 废弃 DateTimePicker 开启插槽开关use-label-slot和use-default-slot ([8d9e5c6](https://github.com/Moonofweisheng/wot-design-uni/commit/8d9e5c66589f1b6eed5faa093f2c28cf2640f5b5))
* ✨ 新增 InputNumber 组件支持长按加减功能 ([#910](https://github.com/Moonofweisheng/wot-design-uni/issues/910)) ([9437087](https://github.com/Moonofweisheng/wot-design-uni/commit/94370876e4ce91faec6e10db5e413dfa27d0f4c2))
* ✨ 重构 Signature 签字板历史记录模式并添加压感模式和横屏示例 ([#967](https://github.com/Moonofweisheng/wot-design-uni/issues/967)) ([aad3e83](https://github.com/Moonofweisheng/wot-design-uni/commit/aad3e8332b4cb6a1f9c0a3c81e9fb7d5068f09b3))
* ✨ Signature 添加历史记录和历史记录步长(包含文档添加、i18n、代码示例) ([#889](https://github.com/Moonofweisheng/wot-design-uni/issues/889)) ([10ec731](https://github.com/Moonofweisheng/wot-design-uni/commit/10ec731b8986f7bf5903a923fe7f1f7d78623ecf))
* extend wd-fab component with 4 new positions and update docs ([6f12aa4](https://github.com/Moonofweisheng/wot-design-uni/commit/6f12aa4b04477a2b1f8535e272699333ceb0b9f6))


### ✏️ Documentation | 文档

* ✏️  接入 petercatai 聊天助手 ([34f0299](https://github.com/Moonofweisheng/wot-design-uni/commit/34f02997dd79f88de89da79791d5a6c056367e8a))
* ✏️  添加优秀案例页面 ([ef7c98b](https://github.com/Moonofweisheng/wot-design-uni/commit/ef7c98bffb843129d872c8ebc6a1bbaf7d8437bf))
* ✏️  新增优秀案例——随享小栈 ([2040230](https://github.com/Moonofweisheng/wot-design-uni/commit/2040230064989bf4816932870920f52ae0797bf2))
* ✏️  修复 Navbar 文档部分标题显示 Tabbar 的问题 ([b4b4d59](https://github.com/Moonofweisheng/wot-design-uni/commit/b4b4d594fc29fe1f947b6ea7e8b5b90cc3f58013))
* ✏️  修复部分文档拼写错误 ([#911](https://github.com/Moonofweisheng/wot-design-uni/issues/911)) ([15613b3](https://github.com/Moonofweisheng/wot-design-uni/commit/15613b393aaf750a85e03512c41ffed473471228))
* fix drop-menu demo code type error ([#905](https://github.com/Moonofweisheng/wot-design-uni/issues/905)) ([0c32c29](https://github.com/Moonofweisheng/wot-design-uni/commit/0c32c29969a8fcb748409a17a06cf277202a18a7))

### [1.7.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.7.0...v1.7.1) (2025-02-19)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 InputNumber 微信小程序设置了precision后无法输入小数点的问题 ([#902](https://github.com/Moonofweisheng/wot-design-uni/issues/902)) ([e3a03b1](https://github.com/Moonofweisheng/wot-design-uni/commit/e3a03b1dbb6640b76a3034daeebd88d42252508c)), closes [#878](https://github.com/Moonofweisheng/wot-design-uni/issues/878)
* 🐛 修复 Upload 文档自定义唤起上传样式的示例错误的问题 ([3dfa69c](https://github.com/Moonofweisheng/wot-design-uni/commit/3dfa69c282514bd786d3822963f3f1dd18089eb9))
* 🐛 修复分页组件为0时不更新页数问题 ([#903](https://github.com/Moonofweisheng/wot-design-uni/issues/903)) ([604faeb](https://github.com/Moonofweisheng/wot-design-uni/commit/604faebf4bb19eca96ba84ee48424be12ee72ba3)), closes [#897](https://github.com/Moonofweisheng/wot-design-uni/issues/897)
* 🐛 修复web-types可选值/默认值类型识别错误 ([#899](https://github.com/Moonofweisheng/wot-design-uni/issues/899)) ([8b4d2ed](https://github.com/Moonofweisheng/wot-design-uni/commit/8b4d2ed14b6340003c63d10e31c02f058bed3ef0))


### ✏️ Documentation | 文档

* ✏️  优化demo项目的展示方便支付宝平台提审 ([c48a67b](https://github.com/Moonofweisheng/wot-design-uni/commit/c48a67b027b9b3f66f0b051ee34bde163feda6cf))

## [1.7.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.6.1...v1.7.0) (2025-02-16)


### ✨ Features | 新功能

* ✨ 添加 web-type.json 支持 webstorm 代码提示 ([#871](https://github.com/Moonofweisheng/wot-design-uni/issues/871)) ([4575099](https://github.com/Moonofweisheng/wot-design-uni/commit/4575099b9ba580fee2c03e8f8660bbfd9aa70aa3)), closes [#819](https://github.com/Moonofweisheng/wot-design-uni/issues/819)
* ✨ 重构 Curtain 优化控制展示隐藏的实现逻辑 ([#895](https://github.com/Moonofweisheng/wot-design-uni/issues/895)) ([0e31950](https://github.com/Moonofweisheng/wot-design-uni/commit/0e3195059e0843045533b7ff89b9a3613c5449a8))
* ✨ ActionSheet 支持 close-on-click-action 控制点击选项后是否关闭菜单功能 ([#891](https://github.com/Moonofweisheng/wot-design-uni/issues/891)) ([a1b035a](https://github.com/Moonofweisheng/wot-design-uni/commit/a1b035a6fe3a9ae3a023032b6c33403853424559)), closes [#698](https://github.com/Moonofweisheng/wot-design-uni/issues/698)
* ✨ NavbarCapsule 导航胶囊组件支持外部传入样式 ([fb980e7](https://github.com/Moonofweisheng/wot-design-uni/commit/fb980e7d9bd90c76570d2069a9d41a6c1187ef30))
* ✨ Rate 评分新增支持半选和触摸滑动选中 ([#896](https://github.com/Moonofweisheng/wot-design-uni/issues/896)) ([9d34f2e](https://github.com/Moonofweisheng/wot-design-uni/commit/9d34f2e5d90cca1820d69100bca7c546ce16fb0e)), closes [#669](https://github.com/Moonofweisheng/wot-design-uni/issues/669)
* ✨ Toast 支持设置布局方向 ([ed60852](https://github.com/Moonofweisheng/wot-design-uni/commit/ed60852432999d295e4af2efe9f1ee86e235e1ee)), closes [#780](https://github.com/Moonofweisheng/wot-design-uni/issues/780)
* ✨ Toast 支持通过props设置组件属性并新增局中显示的配置 ([#888](https://github.com/Moonofweisheng/wot-design-uni/issues/888)) ([ebbe7e4](https://github.com/Moonofweisheng/wot-design-uni/commit/ebbe7e407904df428564dcf53e5141c14d9e7daa))


### ✏️ Documentation | 文档

* ✏️  标注 Form 组件支持不校验隐藏子组件的版本 ([e5a24c4](https://github.com/Moonofweisheng/wot-design-uni/commit/e5a24c4873baa9db62ad1ab6f3adb078dac89dc2))
* ✏️  新增关于页面展示主要团队成员信息 ([031107d](https://github.com/Moonofweisheng/wot-design-uni/commit/031107d5888c2887ea40d47810239cd398f042ef))
* ✏️  修复 SwipeAction 文档示例拼写错误的问题 ([1dc48cf](https://github.com/Moonofweisheng/wot-design-uni/commit/1dc48cfce9773a0ec60dd2b6cb8fd6c60bb63281))
* ✏️  修复文档拼写的错误 ([8f8cb10](https://github.com/Moonofweisheng/wot-design-uni/commit/8f8cb1077aa318e9fb067dfeebdd04dfbdb12412))
* ✏️ fix create-uni command error ([#874](https://github.com/Moonofweisheng/wot-design-uni/issues/874)) ([a0da88c](https://github.com/Moonofweisheng/wot-design-uni/commit/a0da88c5db21b7127de9342aabcf6961b611e22d))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 InputNumber 设置了precision后无法输入小数点的问题 ([#886](https://github.com/Moonofweisheng/wot-design-uni/issues/886)) ([dd1a005](https://github.com/Moonofweisheng/wot-design-uni/commit/dd1a00596472023c9b6e545dbf2a9a8238e67baf)), closes [#878](https://github.com/Moonofweisheng/wot-design-uni/issues/878)
* 🐛 修复 Swiper 在微信小程序平台轮播视频时在iOS平台全屏展示异常的问题 ([#898](https://github.com/Moonofweisheng/wot-design-uni/issues/898)) ([34a15ac](https://github.com/Moonofweisheng/wot-design-uni/commit/34a15ac0786d003825491cc9fa2a8961b307c0d2)), closes [#885](https://github.com/Moonofweisheng/wot-design-uni/issues/885)
* 🐛 修复 Tabs 组件导航地图标题不生效的问题 ([#869](https://github.com/Moonofweisheng/wot-design-uni/issues/869)) ([ccf976a](https://github.com/Moonofweisheng/wot-design-uni/commit/ccf976ad631b4ad0272d4897a6245cdd8f13fa4b))
* 🐛 修复 Upload 组件自定义上传方法不支持asyncfunction的问题 ([#890](https://github.com/Moonofweisheng/wot-design-uni/issues/890)) ([25649db](https://github.com/Moonofweisheng/wot-design-uni/commit/25649dbca55059cb425ba7d7b4e0af7884366dce)), closes [#859](https://github.com/Moonofweisheng/wot-design-uni/issues/859)

### [1.6.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.6.0...v1.6.1) (2025-01-20)


### ✏️ Documentation | 文档

* ✏️  更新文档小程序二维码 ([a5f862b](https://github.com/Moonofweisheng/wot-design-uni/commit/a5f862bdbea6a84df7ad2e3bef4456baae040ae8))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Search placeholder 样式丢失的问题 ([#857](https://github.com/Moonofweisheng/wot-design-uni/issues/857)) ([40565d1](https://github.com/Moonofweisheng/wot-design-uni/commit/40565d19688f6776cdb4ea4b9fc3f6cbaeb4dc66)), closes [#856](https://github.com/Moonofweisheng/wot-design-uni/issues/856)

## [1.6.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.5.1...v1.6.0) (2025-01-18)


### ✏️ Documentation | 文档

* ✏️  常见问题添加Vue 3.3+使用defineOptions设置styleIsolation规则的介绍 ([f3bee13](https://github.com/Moonofweisheng/wot-design-uni/commit/f3bee13b46ccbb8856b9051a99ed2b5356dc0d8e)), closes [#784](https://github.com/Moonofweisheng/wot-design-uni/issues/784)
* ✏️  更新组件库互助群二维码 ([9b50d6c](https://github.com/Moonofweisheng/wot-design-uni/commit/9b50d6c6922e34c964621cff926565cb9fb723ab))
* ✏️  添加互助交流QQ群3群二维码 ([1d0b11b](https://github.com/Moonofweisheng/wot-design-uni/commit/1d0b11b25621b7b585fe1e047093d07861c88a30))
* ✏️  微信小程序演示demo提供激励视频广告页面 ([#783](https://github.com/Moonofweisheng/wot-design-uni/issues/783)) ([7ed7dd3](https://github.com/Moonofweisheng/wot-design-uni/commit/7ed7dd3495c84f91ed5e77870da7e5845fc28a94))
* ✏️  修复 Tooltip 文档显示异常的问题 ([c89eb92](https://github.com/Moonofweisheng/wot-design-uni/commit/c89eb927a21e0f4e10d317346c31ab45d322720d))
* ✏️  演示demo小程序支持分享 ([583acc2](https://github.com/Moonofweisheng/wot-design-uni/commit/583acc2fa942422469abcab5f805a54c72614d69))
* ✏️  优化演示demo支持在顶部显示对应页面微信小程序的二维码 ([b1f42af](https://github.com/Moonofweisheng/wot-design-uni/commit/b1f42af640a32d8c9119331a19fe0495a908b16c))
* ✏️修改自定义样式描述 [#772](https://github.com/Moonofweisheng/wot-design-uni/issues/772) ([1ac352d](https://github.com/Moonofweisheng/wot-design-uni/commit/1ac352da9539e60ac79bd1ec10768e08e4d49ff3))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Calendar 为周选择时跨年周的单元格值显示错误的问题 ([#854](https://github.com/Moonofweisheng/wot-design-uni/issues/854)) ([c0d48b2](https://github.com/Moonofweisheng/wot-design-uni/commit/c0d48b25c11af87629e7feedc6cd0cc3a70b37e4))
* 🐛 修复 Divider 分割线组件 CustomClass 未生效的问题 ([#790](https://github.com/Moonofweisheng/wot-design-uni/issues/790)) ([44df081](https://github.com/Moonofweisheng/wot-design-uni/commit/44df081dbd80771a9df122be4e3953034194308f)), closes [#789](https://github.com/Moonofweisheng/wot-design-uni/issues/789)
* 🐛 修复 Form 表单 validator 校验不通过且未指定错误信息时无法显示校验信息的问题 ([#791](https://github.com/Moonofweisheng/wot-design-uni/issues/791)) ([bdb5653](https://github.com/Moonofweisheng/wot-design-uni/commit/bdb56537db7fbec59224ebf5aecd3e7a6354424c))
* 🐛 修复Button初始化margin的问题 ([#831](https://github.com/Moonofweisheng/wot-design-uni/issues/831)) ([3836309](https://github.com/Moonofweisheng/wot-design-uni/commit/38363097a5fbf4c0c7116abd536f90936d2fa86d))
* 🐛 修复Card footer或者header没写的时候，不会自动隐藏占位 ([8d528cb](https://github.com/Moonofweisheng/wot-design-uni/commit/8d528cb9c06c48456016e0a4cce1cf0699b7311b))
* 🐛 修复Form组件rules属性，没有按照顺序执行问题 ([#808](https://github.com/Moonofweisheng/wot-design-uni/issues/808)) ([834cd8e](https://github.com/Moonofweisheng/wot-design-uni/commit/834cd8e3f08919800189c1d2710267d4aa99a8d5)), closes [#799](https://github.com/Moonofweisheng/wot-design-uni/issues/799) [#799](https://github.com/Moonofweisheng/wot-design-uni/issues/799) [#799](https://github.com/Moonofweisheng/wot-design-uni/issues/799)
* 🐛 修复Input初始化修改失败的问题 ([#814](https://github.com/Moonofweisheng/wot-design-uni/issues/814)) ([04e9a50](https://github.com/Moonofweisheng/wot-design-uni/commit/04e9a50ede30337c35e9f28b7f7985f3e717a91f))
* 🐛 修复Picker文档初始选项code错误的问题 ([67f675d](https://github.com/Moonofweisheng/wot-design-uni/commit/67f675d1cc4764e25d357b91c17ad276612340a1))
* 🐛 修复popover tooltip组件visibleArrow=false时弹出框距离元素间距过远的问题 ([#792](https://github.com/Moonofweisheng/wot-design-uni/issues/792)) ([3b6d10d](https://github.com/Moonofweisheng/wot-design-uni/commit/3b6d10dbb93188adb3aea3fc9f7d1b763b4d5ec9)), closes [#788](https://github.com/Moonofweisheng/wot-design-uni/issues/788)


### ✨ Features | 新功能

* ✨ 新增Signature签名组件 ([0ad8fcc](https://github.com/Moonofweisheng/wot-design-uni/commit/0ad8fcce28f5692572aeae79f0cc7315aa6e5b54)), closes [#505](https://github.com/Moonofweisheng/wot-design-uni/issues/505)
* ✨ 修复 InputNumber 在设置为 allow-null 时被赋值为空时未触发更新的问题并支持异步更新 ([#812](https://github.com/Moonofweisheng/wot-design-uni/issues/812)) ([0fc90dd](https://github.com/Moonofweisheng/wot-design-uni/commit/0fc90ddcc9b5d478fe3e5bf84e2e780c48a8a341))
* ✨ 修复Img组件在错误状态下可以预览的问题 ([dbd2c85](https://github.com/Moonofweisheng/wot-design-uni/commit/dbd2c85b83b0992e791c0b828a12f182d4923b81))
* ✨ Calendar 确认事件 confirm 增加 `type` 参数 ([e0fca91](https://github.com/Moonofweisheng/wot-design-uni/commit/e0fca9161e8282871b2126ecc82999bc9530eb6b))
* ✨ Form 表单 validate 方法支持传入数组 ([#829](https://github.com/Moonofweisheng/wot-design-uni/issues/829)) ([8e6096a](https://github.com/Moonofweisheng/wot-design-uni/commit/8e6096ab7459b9164ef1ec9b366becf9acc7ab83)), closes [#797](https://github.com/Moonofweisheng/wot-design-uni/issues/797)
* ✨ Search新增customInputClass，placeholderClass等属性 ([#845](https://github.com/Moonofweisheng/wot-design-uni/issues/845)) ([03623f4](https://github.com/Moonofweisheng/wot-design-uni/commit/03623f4989b6bfbf53588058c632eb2f2e830bfd)), closes [#299](https://github.com/Moonofweisheng/wot-design-uni/issues/299)
* ✨ Swiper 轮播视频支持控制静音播放和循环播放 ([#855](https://github.com/Moonofweisheng/wot-design-uni/issues/855)) ([7a0d5ce](https://github.com/Moonofweisheng/wot-design-uni/commit/7a0d5ce9720020e3f0fb148f7b65db129ceb82af)), closes [#846](https://github.com/Moonofweisheng/wot-design-uni/issues/846)
* ✨ Swiper 轮播项type字段的处理逻辑限制在可选值范围内 ([#785](https://github.com/Moonofweisheng/wot-design-uni/issues/785)) ([1fd0a27](https://github.com/Moonofweisheng/wot-design-uni/commit/1fd0a27788db2b467bc53e83a6a4953e5d134c53))

### [1.5.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.5.0...v1.5.1) (2024-12-10)


### 🐛 Bug Fixes | Bug 修复

* 🐛 调整 Upload 的覆盖上传参数 reupload 默认值为 false ([d9ce000](https://github.com/Moonofweisheng/wot-design-uni/commit/d9ce00010a15ae8e168e082a2c4f3a50cf61fa13))

## [1.5.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.4.0...v1.5.0) (2024-12-09)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Button 按钮设置为 block 无效的问题 ([#762](https://github.com/Moonofweisheng/wot-design-uni/issues/762)) ([ea8bc66](https://github.com/Moonofweisheng/wot-design-uni/commit/ea8bc6671012a811f49027062e6e7f8f1359a175))
* 🐛 修复 Cell 设置 label 过长时影响页面结构的问题 ([70058f2](https://github.com/Moonofweisheng/wot-design-uni/commit/70058f2270788fb9d7edd56eff35bd2cbebcd99e))
* 🐛 修复 Collapse v-model绑定数据变化时未更新折叠面板状态的问题 ([#744](https://github.com/Moonofweisheng/wot-design-uni/issues/744)) ([09f7f9c](https://github.com/Moonofweisheng/wot-design-uni/commit/09f7f9caf5e381ef44fb9a31965f8d2d70d4e271)), closes [#741](https://github.com/Moonofweisheng/wot-design-uni/issues/741)
* 🐛 修复 GridItem 徽标属性类型标注错误的问题 ([c018560](https://github.com/Moonofweisheng/wot-design-uni/commit/c018560b7d3087b89b759b3c83aff91c74354021)), closes [#766](https://github.com/Moonofweisheng/wot-design-uni/issues/766)
* 🐛 修复 Progress 无法设置进度为 0 的问题 ([#748](https://github.com/Moonofweisheng/wot-design-uni/issues/748)) ([c136f54](https://github.com/Moonofweisheng/wot-design-uni/commit/c136f54cda6164ab3653d47342d7c88c5f515efc)), closes [#747](https://github.com/Moonofweisheng/wot-design-uni/issues/747)
* 🐛 修复 Swiper 在支付宝小程序平台点击事件无效的问题 ([f63bf10](https://github.com/Moonofweisheng/wot-design-uni/commit/f63bf101338d7f9d2f72c5941d3405950544a1d7))
* 🐛 修复 Tab 未渲染项高度会影响整体高度的问题 ([5e06378](https://github.com/Moonofweisheng/wot-design-uni/commit/5e063781a3b58f94f107816473600ce95f3761e9))
* 🐛 修复 vue-tsc 校验不通过的问题 ([#753](https://github.com/Moonofweisheng/wot-design-uni/issues/753)) ([a90f4ad](https://github.com/Moonofweisheng/wot-design-uni/commit/a90f4ad2f2b68bb79f30a2e6973a4e149b7ba66e)), closes [#752](https://github.com/Moonofweisheng/wot-design-uni/issues/752)
* 🐛 修复微信小程序在iOS设备上处于后台一段时间后抖动的问题 ([6091566](https://github.com/Moonofweisheng/wot-design-uni/commit/6091566380c7c129fca284eb4e5a2ba6e447a7ab)), closes [#694](https://github.com/Moonofweisheng/wot-design-uni/issues/694)


### ✏️ Documentation | 文档

* ✏️  调整文档结构增加文档内容可用宽度，支持收起演示demo ([#765](https://github.com/Moonofweisheng/wot-design-uni/issues/765)) ([402f73f](https://github.com/Moonofweisheng/wot-design-uni/commit/402f73f6ee8aa7d022b640333e6bfef4311fdc6f))
* ✏️  添加 MessageBox 自定义按钮样式的文档 ([46b1c39](https://github.com/Moonofweisheng/wot-design-uni/commit/46b1c394a024c293fb07c7788691e2ac572a2fa1))
* ✏️  添加关于Sass的介绍 ([995a65f](https://github.com/Moonofweisheng/wot-design-uni/commit/995a65f8451801062ae83f0f71470d0c428dba7f))
* ✏️  优化 Cell 关于 clickable 和 is-link 的介绍 ([6f58e72](https://github.com/Moonofweisheng/wot-design-uni/commit/6f58e72b1d5436326052a79da19e21d071ab9b3c))
* ✏️  增加 ConfigProvider 组件设定全局共享示例 ([#758](https://github.com/Moonofweisheng/wot-design-uni/issues/758)) ([356cb4a](https://github.com/Moonofweisheng/wot-design-uni/commit/356cb4ad11791366138002233754b2d2e79d5d18))
* ✏️  Table 表格组件提供结合分页器使用的demo ([#738](https://github.com/Moonofweisheng/wot-design-uni/issues/738)) ([fb7580d](https://github.com/Moonofweisheng/wot-design-uni/commit/fb7580df7eee7d81d3826c399e565975cef81625))


### ✨ Features | 新功能

* ✨ 优化 Divider 分割线功能支持虚线、垂直等功能 ([#737](https://github.com/Moonofweisheng/wot-design-uni/issues/737)) ([1b9d7e6](https://github.com/Moonofweisheng/wot-design-uni/commit/1b9d7e625256b365a381b30e902bb54692d636cf))
* ✨ Calendar 优化选中样式和滚动位置处理并支持屏蔽内置cell ([#768](https://github.com/Moonofweisheng/wot-design-uni/issues/768)) ([97c4004](https://github.com/Moonofweisheng/wot-design-uni/commit/97c40047e8ed46af31d4c1647056d90c2edf4842))
* ✨ Curtain 幕帘新增 close 插槽和自定义关闭插槽样式类 ([#746](https://github.com/Moonofweisheng/wot-design-uni/issues/746)) ([7bc3592](https://github.com/Moonofweisheng/wot-design-uni/commit/7bc359205deb99899baf01c733af9690b12703fa)), closes [#648](https://github.com/Moonofweisheng/wot-design-uni/issues/648)
* ✨ DropMenuItem 增加自定 Popup 样式参数 ([28ad03b](https://github.com/Moonofweisheng/wot-design-uni/commit/28ad03b7afdd38a86f168d15a7c0f3564d122101))
* ✨ Input、Textarea在APP-VUE和H5端支持inputmode ([#771](https://github.com/Moonofweisheng/wot-design-uni/issues/771)) ([9ceb2e8](https://github.com/Moonofweisheng/wot-design-uni/commit/9ceb2e807e33edd3937db5f57a4306f1ce719cff)), closes [#743](https://github.com/Moonofweisheng/wot-design-uni/issues/743)
* ✨ MessageBox 新增确认、取消按钮的 ButtonProps 属性 ([#761](https://github.com/Moonofweisheng/wot-design-uni/issues/761)) ([80682ba](https://github.com/Moonofweisheng/wot-design-uni/commit/80682ba933427fac7bfefc6c692cd058a14163c5)), closes [#740](https://github.com/Moonofweisheng/wot-design-uni/issues/740)
* ✨ Radio添加icon-placement属性用于控制图标方向 ([#763](https://github.com/Moonofweisheng/wot-design-uni/issues/763)) ([b06a7a7](https://github.com/Moonofweisheng/wot-design-uni/commit/b06a7a751b3115c8e3909af4cbe595684ed9d00f)), closes [#371](https://github.com/Moonofweisheng/wot-design-uni/issues/371)
* ✨ Segmented 提供 updateActiveStyle 方法设置激活样式 ([529e57f](https://github.com/Moonofweisheng/wot-design-uni/commit/529e57fc83b00482f101e84cf0437627e140a59f))
* ✨ Table 支持设置不固定表头 ([#769](https://github.com/Moonofweisheng/wot-design-uni/issues/769)) ([b0a2461](https://github.com/Moonofweisheng/wot-design-uni/commit/b0a2461a1a6f9691502a1f4a1a06ec4103fabd4b))
* ✨ Upload 支持文件重传 ([3cd5137](https://github.com/Moonofweisheng/wot-design-uni/commit/3cd5137129ae13f90744a5d0038686cef5602d8c))

## [1.4.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.14...v1.4.0) (2024-11-24)


### ✨ Features | 新功能

* ✨ Curtain 幕帘组件支持设置 z-index ([a1e20af](https://github.com/Moonofweisheng/wot-design-uni/commit/a1e20afef34704e518a96d4b4cb248cab3b7a8b6))
* ✨ Siderbar 侧边栏添加before-change属性支持异步更新 ([#721](https://github.com/Moonofweisheng/wot-design-uni/issues/721)) ([1f5801d](https://github.com/Moonofweisheng/wot-design-uni/commit/1f5801d3f4c2cfc1b3990d74077e18ea88e8d7f8)), closes [#711](https://github.com/Moonofweisheng/wot-design-uni/issues/711)
* ✨ Swiper 支持指定轮播项的文件类型 ([#720](https://github.com/Moonofweisheng/wot-design-uni/issues/720)) ([1e039cb](https://github.com/Moonofweisheng/wot-design-uni/commit/1e039cb7073d57bd19f59d4ffbb95c74b5cdc42f)), closes [#712](https://github.com/Moonofweisheng/wot-design-uni/issues/712)
* ✨ Tab 添加 lazy 属性支持配置是否开启懒加载 ([bb5b193](https://github.com/Moonofweisheng/wot-design-uni/commit/bb5b19325fc2a0f4d13d353ee9bc8cfbf8605daa)), closes [#641](https://github.com/Moonofweisheng/wot-design-uni/issues/641)
* ✨ Tabs 新增 `autoLineWidth` 设置底部条宽度自动同步文本内容' ([#679](https://github.com/Moonofweisheng/wot-design-uni/issues/679)) ([cb7cbf3](https://github.com/Moonofweisheng/wot-design-uni/commit/cb7cbf33250e3711d631271b1bbcb5f6829e75fa))
* ✨ Tabs 新增map-title属性支持修改导航地图标题 ([0b7605f](https://github.com/Moonofweisheng/wot-design-uni/commit/0b7605f3ef2ca11cedc2cc61a3eb93e0757e6b86)), closes [#670](https://github.com/Moonofweisheng/wot-design-uni/issues/670)
* ✨ Tabs 支持设置徽标 ([#724](https://github.com/Moonofweisheng/wot-design-uni/issues/724)) ([cd20581](https://github.com/Moonofweisheng/wot-design-uni/commit/cd20581ca6f75a67995a0cf562f524d82e357bbc)), closes [#689](https://github.com/Moonofweisheng/wot-design-uni/issues/689) [#672](https://github.com/Moonofweisheng/wot-design-uni/issues/672)
* ✨ tabs支持左对齐 ([#718](https://github.com/Moonofweisheng/wot-design-uni/issues/718)) ([314c2e8](https://github.com/Moonofweisheng/wot-design-uni/commit/314c2e8c9d08e806dd0ec78fd4b2aa5e536af8f8)), closes [#380](https://github.com/Moonofweisheng/wot-design-uni/issues/380)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Collapse 使用 toggleAall 方法时不会触发 before-expand 钩子的问题 ([#727](https://github.com/Moonofweisheng/wot-design-uni/issues/727)) ([02aa5ce](https://github.com/Moonofweisheng/wot-design-uni/commit/02aa5ceb78713f210d97ecc29f18618084b93096))
* 🐛 修复 CollapseItem 在微信小程序平台使用 title 插槽时宽度无法撑满的问题 ([4f1d945](https://github.com/Moonofweisheng/wot-design-uni/commit/4f1d9452ecd5054a636fb51871369ee1f183e1f1))
* 🐛 修复 DateTimePicker 设置为 time 类型时绑定值无法设置为空数组的问题 ([443ac92](https://github.com/Moonofweisheng/wot-design-uni/commit/443ac929820327339062a608ef94db43bdaafb27)), closes [#706](https://github.com/Moonofweisheng/wot-design-uni/issues/706)
* 🐛 修复 FloadingPanel 设置 height 不生效的问题 ([#725](https://github.com/Moonofweisheng/wot-design-uni/issues/725)) ([3cc1805](https://github.com/Moonofweisheng/wot-design-uni/commit/3cc18058aee83ed0abbf804595e38d7934490f4a)), closes [#703](https://github.com/Moonofweisheng/wot-design-uni/issues/703)
* 🐛 修复 Slider 滑块处于极值时会遮挡max和min的问题 ([aa8834d](https://github.com/Moonofweisheng/wot-design-uni/commit/aa8834df630286882425778263ea9ff68811c928)), closes [#714](https://github.com/Moonofweisheng/wot-design-uni/issues/714)
* 🐛 修复 wd-select-picker 组件单选搜索高亮 class 错误 ([7d461a5](https://github.com/Moonofweisheng/wot-design-uni/commit/7d461a54d09b14273fd18ff37310ecf754cf5138))
* 🐛 修复Collapse折叠面板组件内容溢出问题 ([#710](https://github.com/Moonofweisheng/wot-design-uni/issues/710)) ([4bf8d1e](https://github.com/Moonofweisheng/wot-design-uni/commit/4bf8d1e300c28f405402582dd32fde4245d9ed47))


### ✏️ Documentation | 文档

* ✏️  调整文档中gitee镜像仓库的地址 ([a40dd9f](https://github.com/Moonofweisheng/wot-design-uni/commit/a40dd9f63e52cf178be2fd9a93904cdf26e23f14))
* ✏️  调整join-group页面加群的问题 ([9a70d89](https://github.com/Moonofweisheng/wot-design-uni/commit/9a70d89be1e369dc2ea2a4babd3b3fdb326fcecc))
* ✏️  添加 Cell 单元格 border 属性的文档 ([fad777d](https://github.com/Moonofweisheng/wot-design-uni/commit/fad777dfa8cf8dcf26288a9f975927dc3de2e80d)), closes [#653](https://github.com/Moonofweisheng/wot-design-uni/issues/653)
* ✏️  添加关于 Button 自定义样式设置阴影的文档 ([c38321f](https://github.com/Moonofweisheng/wot-design-uni/commit/c38321f69c8f4fcb572cb40c8eef38cf4fa20eae)), closes [#731](https://github.com/Moonofweisheng/wot-design-uni/issues/731)
* ✏️  文档新增演示页面源码和组件源码链接 ([330e8c7](https://github.com/Moonofweisheng/wot-design-uni/commit/330e8c7bbfc792f4a29cdaa08dec8b35c75b2a30))
* ✏️  优化 NoticeBar 垂直滚动示例 ([ff1d377](https://github.com/Moonofweisheng/wot-design-uni/commit/ff1d37723b38169fc1a1f75676a8464343b42bec))
* ✏️  增加 Input 字数限制的示例 ([5aa1c00](https://github.com/Moonofweisheng/wot-design-uni/commit/5aa1c00acd72f17cea108ffccecc427711e2cbdb))
* ✏️  增加关于Tabs属性autoLineWidth的介绍和最低版本要求 ([08e7d77](https://github.com/Moonofweisheng/wot-design-uni/commit/08e7d774c6b6886fe3f79431b4728176e267144f))

### [1.3.14](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.13...v1.3.14) (2024-11-09)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Curtain 幕帘组件在某些情况下关闭按钮不显示的问题 ([8c0e978](https://github.com/Moonofweisheng/wot-design-uni/commit/8c0e97831445183662ce0af79210117eb77e63e9)), closes [#690](https://github.com/Moonofweisheng/wot-design-uni/issues/690)
* 🐛 修复Picker和SelectPicker清空按钮颜色与Input不统一的问题 ([#700](https://github.com/Moonofweisheng/wot-design-uni/issues/700)) ([8fdbfa3](https://github.com/Moonofweisheng/wot-design-uni/commit/8fdbfa319a5c0c5895a3162b56cd3225c54a24d2))
* 🐛 修复upload组件header默认值错误的问题 ([4cfd1e5](https://github.com/Moonofweisheng/wot-design-uni/commit/4cfd1e568d55eed9efe9cc1dadcc30dd571d3b36)), closes [#691](https://github.com/Moonofweisheng/wot-design-uni/issues/691)


### ✏️ Documentation | 文档

* ✏️  文档添加生活小工具 ([6f44a63](https://github.com/Moonofweisheng/wot-design-uni/commit/6f44a631ac067a7321ff0cb6ea8ebaabbc64bd9b))
* ✏️  文档中添加wot-demo的介绍 ([66fff60](https://github.com/Moonofweisheng/wot-design-uni/commit/66fff6009b261a62be96d4c279d83833c70a8a0f))
* ✏️  增加create-uni创建快速上手项目的介绍 ([92aa1ef](https://github.com/Moonofweisheng/wot-design-uni/commit/92aa1efe68f4a9bd52942122b2f063384eb885fc))


### ✨ Features | 新功能

* ✨ 使用Transition重构Popup为center类型的Popup添加zoom-in动画 ([#699](https://github.com/Moonofweisheng/wot-design-uni/issues/699)) ([0dd34d0](https://github.com/Moonofweisheng/wot-design-uni/commit/0dd34d06492f9d071ce6c11aa82789fbcc5cd442)), closes [#687](https://github.com/Moonofweisheng/wot-design-uni/issues/687)
* ✨ 移除Switch默认的size支持在不指定size的情况下使用css变量设置组件尺寸 ([5e55da4](https://github.com/Moonofweisheng/wot-design-uni/commit/5e55da4839677c63534148d4664dbde1c9f950b2)), closes [#516](https://github.com/Moonofweisheng/wot-design-uni/issues/516)
* ✨ 优化Toast、Message和Notify组件的函数式调用方案 ([#696](https://github.com/Moonofweisheng/wot-design-uni/issues/696)) ([9f318bd](https://github.com/Moonofweisheng/wot-design-uni/commit/9f318bdeb37bea643276d3e7f8be51bfc1b19d27))
* ✨ 支持Button在支付宝小程序平台opentype设为getAuthorize用于获取手机号和用户信息 ([deeb45d](https://github.com/Moonofweisheng/wot-design-uni/commit/deeb45d8cb47284c1a557b50c3fcd95f80f93c22))
* ✨ Form 校验规则validator支持传入Error作为校验提示 ([db32ef9](https://github.com/Moonofweisheng/wot-design-uni/commit/db32ef962140333a13e2a04ba4642e7423bc4bef)), closes [#667](https://github.com/Moonofweisheng/wot-design-uni/issues/667)
* ✨ Loadmore提供loadingProps属性用于自定义loading样式 ([178e056](https://github.com/Moonofweisheng/wot-design-uni/commit/178e056035511de4123d2bd9ce575948154874b4))
* ✨ Table 行高支持Number和String类型 ([#682](https://github.com/Moonofweisheng/wot-design-uni/issues/682)) ([21f0b17](https://github.com/Moonofweisheng/wot-design-uni/commit/21f0b178b6e3221a69609dd1603960fc866cb534))

### [1.3.13](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.12...v1.3.13) (2024-10-25)


### ✏️ Documentation | 文档

* ✏️  快速上手增加vue和uni-app的快速上手链接 ([20148a7](https://github.com/Moonofweisheng/wot-design-uni/commit/20148a7800d12376adedadb32bf29239f02baef3))
* ✏️  添加关于深度选择的介绍 ([63428f2](https://github.com/Moonofweisheng/wot-design-uni/commit/63428f244ae84574027ea89e19598c3600716f7b))
* ✏️  文档新增展示优秀案例 ([47c1764](https://github.com/Moonofweisheng/wot-design-uni/commit/47c176490dabda1937abea8342d02cc6cbbcfc02))
* ✏️  优化文档快速上手章节 ([4e9a9da](https://github.com/Moonofweisheng/wot-design-uni/commit/4e9a9da914a372f1b0f31cfcc7fbc2f96650ef58))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Notice在Tabbar页面时跳转至其他页面导致播放异常的问题并提供reset方法 ([#680](https://github.com/Moonofweisheng/wot-design-uni/issues/680)) ([7584ac2](https://github.com/Moonofweisheng/wot-design-uni/commit/7584ac2a1249b6bee79669ae57c80da08a17d912)), closes [#358](https://github.com/Moonofweisheng/wot-design-uni/issues/358) [#650](https://github.com/Moonofweisheng/wot-design-uni/issues/650)


### ✨ Features | 新功能

* ✨ 为Picker和SelectPicker补充clear事件 ([8fffaa6](https://github.com/Moonofweisheng/wot-design-uni/commit/8fffaa646aad195b33c56719ffb28b2529f3f627))
* ✨ 移除 Navbar 点击热区的激活态样式 ([60b07e5](https://github.com/Moonofweisheng/wot-design-uni/commit/60b07e514b836c55305536e2ba29ec5239b62de3)), closes [#511](https://github.com/Moonofweisheng/wot-design-uni/issues/511)
* ✨ cell组件border属性以props为最高优先级 ([#656](https://github.com/Moonofweisheng/wot-design-uni/issues/656)) ([31353ce](https://github.com/Moonofweisheng/wot-design-uni/commit/31353ceafa3bcae01202c40918e579d141957c0a))
* ✨ picker和selectPicker添加clearable属性 ([b0d60a0](https://github.com/Moonofweisheng/wot-design-uni/commit/b0d60a0b750e8ed2cbae0b55309145c6963e498a))
* ✨ Swiper 轮播组件支持展示视频和设置轮播项标题 ([#663](https://github.com/Moonofweisheng/wot-design-uni/issues/663)) ([a50c0be](https://github.com/Moonofweisheng/wot-design-uni/commit/a50c0be38465342e5b688b8e10b377d69ba998a6))

### [1.3.12](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.11...v1.3.12) (2024-10-08)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Upload文件小程序找不到文件,显示的问题 ([c13e605](https://github.com/Moonofweisheng/wot-design-uni/commit/c13e6058a2c665c174806aebd353294113c4007e))
* 🐛 Radio修复在cell里面高度的问题 ([f9deacf](https://github.com/Moonofweisheng/wot-design-uni/commit/f9deacf40eed17a37558ca96319880822ca897d5))


### ✏️ Documentation | 文档

* ✏️  优化捐赠榜单中捐赠人链接的展示效果 ([898f079](https://github.com/Moonofweisheng/wot-design-uni/commit/898f07985e602ad18a84c06b5bd6183dd61142cd))
* ✏️  Upload添加preview-cover最低版本 ([52f9bf6](https://github.com/Moonofweisheng/wot-design-uni/commit/52f9bf654b26a09c20b7118efb331f7e6228ea79))


### ✨ Features | 新功能

* ✨ 新增 FloatingPanel 浮动面板组件 ([#616](https://github.com/Moonofweisheng/wot-design-uni/issues/616)) ([e2966fd](https://github.com/Moonofweisheng/wot-design-uni/commit/e2966fdd01d6c91ab9499fbc95e4f7160a83deb5)), closes [#509](https://github.com/Moonofweisheng/wot-design-uni/issues/509)
* ✨ 新增支持法语、日语等9 种语言 ([#637](https://github.com/Moonofweisheng/wot-design-uni/issues/637)) ([691a7b5](https://github.com/Moonofweisheng/wot-design-uni/commit/691a7b57727af45f3c2f99437740be72e8be0f86))
* ✨ StatusTip缺省提示组件提供图片内容插槽([#538](https://github.com/Moonofweisheng/wot-design-uni/issues/538)) ([#615](https://github.com/Moonofweisheng/wot-design-uni/issues/615)) ([c6b2cf8](https://github.com/Moonofweisheng/wot-design-uni/commit/c6b2cf84db1cb5536516606999c1fa3d6bd1dbe3))
* ✨ ToolTip 组件 offset 属性支持数组和对象写法 ([#625](https://github.com/Moonofweisheng/wot-design-uni/issues/625)) ([5092c5a](https://github.com/Moonofweisheng/wot-design-uni/commit/5092c5a6548fe7222e0d6e2614020f15ce95c5df)), closes [#560](https://github.com/Moonofweisheng/wot-design-uni/issues/560)
* ✨ Upload新增preview-cover插槽 ([ef6433d](https://github.com/Moonofweisheng/wot-design-uni/commit/ef6433d81c576db8b55715a14a574a6d392478df))
* ✨Calendar日历添加open事件 ([#627](https://github.com/Moonofweisheng/wot-design-uni/issues/627)) ([43a5da6](https://github.com/Moonofweisheng/wot-design-uni/commit/43a5da631d4402319d4e3f0739f2ab7e960e497f)), closes [#624](https://github.com/Moonofweisheng/wot-design-uni/issues/624)

### [1.3.11](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.10...v1.3.11) (2024-09-23)


### ✏️ Documentation | 文档

* ✏️  移除文档中CountTo不支持的type ([cae2e56](https://github.com/Moonofweisheng/wot-design-uni/commit/cae2e56aba98427b1b6c7a2c3f6a2f9ed8acf96f))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复[#598](https://github.com/Moonofweisheng/wot-design-uni/issues/598)产生的使用未定义变量的问题 ([ce64daf](https://github.com/Moonofweisheng/wot-design-uni/commit/ce64daf77d119df509873226c9209cae406e0330))
* 🐛 修复与@uni-helper/uni-typed搭配使用时tsc报类型错误的问题并更新依赖 ([f9ca2e4](https://github.com/Moonofweisheng/wot-design-uni/commit/f9ca2e4d4ed2ef88a073f7f80ce64df811144b3a)), closes [#586](https://github.com/Moonofweisheng/wot-design-uni/issues/586)
* 🐛 修复Button设置为hairline时圆角显示异常的问题 ([10ebf5c](https://github.com/Moonofweisheng/wot-design-uni/commit/10ebf5c3959099389f1f1349e32ad755740ce0d5)), closes [#486](https://github.com/Moonofweisheng/wot-design-uni/issues/486)
* 🐛 修复Input、Textarea未设置labelWidth时通过CSS变量设置label宽度无效的问题 ([#591](https://github.com/Moonofweisheng/wot-design-uni/issues/591)) ([2f12ac6](https://github.com/Moonofweisheng/wot-design-uni/commit/2f12ac6d2b598c7fa545009a82acd1c07bf21779)), closes [#573](https://github.com/Moonofweisheng/wot-design-uni/issues/573)
* 🐛 修复loadmore组件属性errorText存在默认值导致国际化失效的问题 ([#594](https://github.com/Moonofweisheng/wot-design-uni/issues/594)) ([0bcc3c4](https://github.com/Moonofweisheng/wot-design-uni/commit/0bcc3c498ed9206bea45522f58889ec3f3a5f673))
* 🐛 修复Radio点击失效的问题 ([5cce125](https://github.com/Moonofweisheng/wot-design-uni/commit/5cce125c737989e1e447394223129e2e585b91f4)), closes [#596](https://github.com/Moonofweisheng/wot-design-uni/issues/596)
* 🐛确保inputNumber输入值在设定的最小值和最大值之间 ([#610](https://github.com/Moonofweisheng/wot-design-uni/issues/610)) ([344b1ac](https://github.com/Moonofweisheng/wot-design-uni/commit/344b1ac9168701bc408f82268f68ef8453527ef9)), closes [#602](https://github.com/Moonofweisheng/wot-design-uni/issues/602)


### ✨ Features | 新功能

* ✨ 迁移StatusTip组件的图片资源 ([f54ff30](https://github.com/Moonofweisheng/wot-design-uni/commit/f54ff306e7dafcfaead671120d100c4d9482daf9))
* ✨ count-to组件添加type属性 ([#556](https://github.com/Moonofweisheng/wot-design-uni/issues/556)) ([60c92f2](https://github.com/Moonofweisheng/wot-design-uni/commit/60c92f2f38b563968f88f94267b8d805c28109d1))
* ✨ Fab 悬浮按钮组件支持自定义触发器和控制能否展开 ([#612](https://github.com/Moonofweisheng/wot-design-uni/issues/612)) ([8e68ef3](https://github.com/Moonofweisheng/wot-design-uni/commit/8e68ef3bf807b5ec3c935daa6ce68f58962a188d)), closes [#512](https://github.com/Moonofweisheng/wot-design-uni/issues/512)
* ✨ Img组件新增属性 show-menu-by-longpress 支持微信小程序长按弹出菜单栏 ([#613](https://github.com/Moonofweisheng/wot-design-uni/issues/613)) ([a7fc229](https://github.com/Moonofweisheng/wot-design-uni/commit/a7fc22964823af1ee1e513a268fb783bbea77ee4)), closes [#611](https://github.com/Moonofweisheng/wot-design-uni/issues/611)
* ✨ Input、Textarea增加ignoreCompositionEvent属性 ([#592](https://github.com/Moonofweisheng/wot-design-uni/issues/592)) ([efcd4bb](https://github.com/Moonofweisheng/wot-design-uni/commit/efcd4bbb38b5bf53a26f0a8834f0cbd9fa26f5e5)), closes [#574](https://github.com/Moonofweisheng/wot-design-uni/issues/574)
* ✨ InputNumber 步进器新增支持adjustPosition属性 ([f8a5240](https://github.com/Moonofweisheng/wot-design-uni/commit/f8a524010a2945941fbefa745756f2e6efbeb88b)), closes [#599](https://github.com/Moonofweisheng/wot-design-uni/issues/599)

### [1.3.10](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.9...v1.3.10) (2024-09-08)


### ✨ Features | 新功能

* ✨ 新增Keyboard虚拟键盘支持车牌号输入 ([#567](https://github.com/Moonofweisheng/wot-design-uni/issues/567)) ([59df1b7](https://github.com/Moonofweisheng/wot-design-uni/commit/59df1b7ce56e9253ad046a7898651a866b8c99d7)), closes [#351](https://github.com/Moonofweisheng/wot-design-uni/issues/351)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 col-picker 点击遮罩关闭执行两次问题 ([#584](https://github.com/Moonofweisheng/wot-design-uni/issues/584)) ([8fc5380](https://github.com/Moonofweisheng/wot-design-uni/commit/8fc5380ad1f9a34998aedf1de2a9b05011bd8267))
* 🐛 修复wd-upload 关闭按钮受项目行高影响的问题 ([#576](https://github.com/Moonofweisheng/wot-design-uni/issues/576)) ([feb64ea](https://github.com/Moonofweisheng/wot-design-uni/commit/feb64ea7be1eada01782bb1fa0d5b848acb4fa64))


### ✏️ Documentation | 文档

* ✏️  文档中添加KeyBoard的版本要求 ([715c054](https://github.com/Moonofweisheng/wot-design-uni/commit/715c054f4c723de73e2d57b940390cc7bcc89273))
* ✏️  优化多列选择器组件文档 ([#572](https://github.com/Moonofweisheng/wot-design-uni/issues/572)) ([93e0736](https://github.com/Moonofweisheng/wot-design-uni/commit/93e0736a24aa77d60376b5dd4d073180b88a6411))

### [1.3.9](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.8...v1.3.9) (2024-09-01)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复某些场景下wd-textarea属性maxlength无效的问题 ([#558](https://github.com/Moonofweisheng/wot-design-uni/issues/558)) ([4b1b237](https://github.com/Moonofweisheng/wot-design-uni/commit/4b1b2370db4a34716ceedfde3a49d2ae7e20f919))
* 🐛 修复IndexBar组件更新数据时显示异常的问题 ([f4c751d](https://github.com/Moonofweisheng/wot-design-uni/commit/f4c751dfae3724118c2d842489a5c7d1003996f1)), closes [#545](https://github.com/Moonofweisheng/wot-design-uni/issues/545)
* 🐛 修复Input和Textarea设置为readonly时在部分iOS手机可以点击聚焦的问题 ([ff59403](https://github.com/Moonofweisheng/wot-design-uni/commit/ff594031546a25f4b2831749312171d260a76044)), closes [#533](https://github.com/Moonofweisheng/wot-design-uni/issues/533)
* 🐛 修复Picker多级联动未应用picker选择值时取消后打开展示列数据错误的问题 ([90e86de](https://github.com/Moonofweisheng/wot-design-uni/commit/90e86def95ae458208f8aa50427b845db61d05b4)), closes [#562](https://github.com/Moonofweisheng/wot-design-uni/issues/562)


### ✏️ Documentation | 文档

* ✏️  调整数字滚动组件文档路径 ([636a2c5](https://github.com/Moonofweisheng/wot-design-uni/commit/636a2c5e8a8906166947312f085e5fabba5c47df))
* ✏️  更正form demo 方法名不一致 ([#534](https://github.com/Moonofweisheng/wot-design-uni/issues/534)) ([59fb1ea](https://github.com/Moonofweisheng/wot-design-uni/commit/59fb1eaabd7fea5428dc860af5ad734c9eebb496))
* ✏️  优化MessageBox示例demo ([a827b79](https://github.com/Moonofweisheng/wot-design-uni/commit/a827b79498a48272eb71fd9c0e70a9dae889375b))
* ✏️  增加安卓演示demo下载二维码 ([8680e77](https://github.com/Moonofweisheng/wot-design-uni/commit/8680e776e229d96b995de402e12ab423930a27ba))
* ✏️ 更正text组件doc的type属性默认值 ([#557](https://github.com/Moonofweisheng/wot-design-uni/issues/557)) ([3e51f76](https://github.com/Moonofweisheng/wot-design-uni/commit/3e51f763cf394cd5214bc5c0c3e6e23c9ee974b7))


### ✨ Features | 新功能

* ✨ config-provider 支持customClass 与 customeStyle ([#535](https://github.com/Moonofweisheng/wot-design-uni/issues/535)) ([febe730](https://github.com/Moonofweisheng/wot-design-uni/commit/febe73079eceeb2cf0eec9fc5712321fa3434bd2))
* ✨ Form设置提示模式为toast时优先显示顺序靠前的表单项的错误提示 ([f25409f](https://github.com/Moonofweisheng/wot-design-uni/commit/f25409f5a83df50ecbbf0a21bb2d5199021b9cc1)), closes [#548](https://github.com/Moonofweisheng/wot-design-uni/issues/548)
* ✨ img 图片组件click事件增加mouseEvent参数 ([#539](https://github.com/Moonofweisheng/wot-design-uni/issues/539)) ([4983f48](https://github.com/Moonofweisheng/wot-design-uni/commit/4983f4832a194d399aeb919b8d2c02564789d9ab))
* ✨ Text支持传入number类型的text ([e1f9ff6](https://github.com/Moonofweisheng/wot-design-uni/commit/e1f9ff60e35c7b6bbb4b5e7b84b2d570471056f6)), closes [#553](https://github.com/Moonofweisheng/wot-design-uni/issues/553)

### [1.3.8](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.7...v1.3.8) (2024-08-18)


### ✏️ Documentation | 文档

* ✏️  补充Form设置error-type的示例 ([0e6ea3e](https://github.com/Moonofweisheng/wot-design-uni/commit/0e6ea3ead02c7b51fac7c5776c92044abeaa4dc3))
* ✏️  修复常见问题中useToast入参错误的问题 ([3cd0905](https://github.com/Moonofweisheng/wot-design-uni/commit/3cd0905bab2b0dfc3d05257e01aa16afe61c0002))
* ✏️  修复textarea组件的maxlength属性类型标注错误 ([#502](https://github.com/Moonofweisheng/wot-design-uni/issues/502)) ([3698e30](https://github.com/Moonofweisheng/wot-design-uni/commit/3698e30301c6c482135671b0a3f150388421ffae))
* ✏️  优化MessageBox和Toast文档中selector的介绍 ([f092838](https://github.com/Moonofweisheng/wot-design-uni/commit/f092838756144677c97155dfb700b1ae2cf56f47))
* ✏️ Toast 文档增加唯一标识selector的介绍 ([#494](https://github.com/Moonofweisheng/wot-design-uni/issues/494)) ([3f99e1b](https://github.com/Moonofweisheng/wot-design-uni/commit/3f99e1bfc7e78c0359e593a2862d1d885c914858))


### ✨ Features | 新功能

* ✨ 调整Statistic为CountTo组件并使用useCountDown重构 ([a01baaf](https://github.com/Moonofweisheng/wot-design-uni/commit/a01baafe327e4feeba0de9735901dfc32e014f6a))
* ✨ 新增statistic 数值显示 ([#489](https://github.com/Moonofweisheng/wot-design-uni/issues/489)) ([592c37b](https://github.com/Moonofweisheng/wot-design-uni/commit/592c37b15e54049faefd14cb480451895bb34d90))
* ✨ Collapse 折叠面板组件支持嵌套使用 ([#521](https://github.com/Moonofweisheng/wot-design-uni/issues/521)) ([a5ce5dd](https://github.com/Moonofweisheng/wot-design-uni/commit/a5ce5dd8825e520f8e676a9f400efda1eb27546a)), closes [#503](https://github.com/Moonofweisheng/wot-design-uni/issues/503)
* ✨ datetime-picker 增加自定义 Cell 样式属性 ([#491](https://github.com/Moonofweisheng/wot-design-uni/issues/491)) ([2cc7d7f](https://github.com/Moonofweisheng/wot-design-uni/commit/2cc7d7fb106ec0a13104bf8bea964ba375bd7ad5))
* ✨ form表单新增errorType错误提示类型 ([#487](https://github.com/Moonofweisheng/wot-design-uni/issues/487)) ([5915922](https://github.com/Moonofweisheng/wot-design-uni/commit/5915922b0b4970424e2dc97ef7f922a918fedfce))
* ✨Upload 组件支持手动上传并增加自定义上传函数 ([3def17e](https://github.com/Moonofweisheng/wot-design-uni/commit/3def17eb16b1355ee0a9a950788ef6299b0179a3)), closes [#481](https://github.com/Moonofweisheng/wot-design-uni/issues/481)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复checkbox-group的shape无法作用到子组件的问题 ([cd96d25](https://github.com/Moonofweisheng/wot-design-uni/commit/cd96d258f618d4a93584307b4100eee448a58884)), closes [#519](https://github.com/Moonofweisheng/wot-design-uni/issues/519)
* 🐛 修复DropDown传入正确direction出现错误警告的问题 ([#499](https://github.com/Moonofweisheng/wot-design-uni/issues/499)) ([35507a1](https://github.com/Moonofweisheng/wot-design-uni/commit/35507a1b24dbdee27eefb960ffa637e842b04f9c))
* 🐛 修复picker-view动态设置columns时获取选中值异常的问题 ([#518](https://github.com/Moonofweisheng/wot-design-uni/issues/518)) ([8530440](https://github.com/Moonofweisheng/wot-design-uni/commit/8530440e81cb8244d7e6288a2c7a8fda41bc7a11)), closes [#492](https://github.com/Moonofweisheng/wot-design-uni/issues/492)
* 🐛 修复Textarea组件暗黑模式下清空按钮样式不正确的问题 ([8e83a11](https://github.com/Moonofweisheng/wot-design-uni/commit/8e83a117f2acf8b17b021ec833b43d9b2b940d9a))

### [1.3.7](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.6...v1.3.7) (2024-08-06)


### ✏️ Documentation | 文档

* ✏️  更新爱发电地址和示例demo二维码 ([0ecc4c1](https://github.com/Moonofweisheng/wot-design-uni/commit/0ecc4c194d753a11dfa461d74df1a00d75be0e4e))
* ✏️  更新README ([0c33dd4](https://github.com/Moonofweisheng/wot-design-uni/commit/0c33dd48ad8528b4b080125272375bae9fedf352))
* ✏️  允许文档组件列表折叠收起 ([#468](https://github.com/Moonofweisheng/wot-design-uni/issues/468)) ([b0e4d23](https://github.com/Moonofweisheng/wot-design-uni/commit/b0e4d235b27a729024951a7b31950e83bd43d3de))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复addUnit工具方法为string类型的参数时未添加单位导致swiper高度丢失的问题 ([3d7775c](https://github.com/Moonofweisheng/wot-design-uni/commit/3d7775c5a93668cb7ac6b50563869d13bbd39bfa))
* 🐛 修复Input设置为number类型时绑定值重设为0时显示异常的问题 ([df6a6a0](https://github.com/Moonofweisheng/wot-design-uni/commit/df6a6a0ab1f911296002e39299a93bbee5546715)), closes [#470](https://github.com/Moonofweisheng/wot-design-uni/issues/470)
* 🐛 修复LockScroll后切换页面无法滚动 ([#478](https://github.com/Moonofweisheng/wot-design-uni/issues/478)) ([197d61a](https://github.com/Moonofweisheng/wot-design-uni/commit/197d61a678738bda608588d660263e0d5657f940)), closes [#473](https://github.com/Moonofweisheng/wot-design-uni/issues/473)
* 🐛 修复Textarea的placeholder无法设置空字符串问题 ([#472](https://github.com/Moonofweisheng/wot-design-uni/issues/472)) ([bb3d329](https://github.com/Moonofweisheng/wot-design-uni/commit/bb3d3292af56016ad21d7bf49024a0338d93ec3d)), closes [#471](https://github.com/Moonofweisheng/wot-design-uni/issues/471)
* **type:** 🐛 修复vue>=2.7版本的GlobalComponents类型声明问题 ([#464](https://github.com/Moonofweisheng/wot-design-uni/issues/464)) ([a175f05](https://github.com/Moonofweisheng/wot-design-uni/commit/a175f05e01eff86678dd08bd226bd401192b0c0b))


### ✨ Features | 新功能

* ✨ 修复Text组件设置color属性后lines失效的问题 ([84826f8](https://github.com/Moonofweisheng/wot-design-uni/commit/84826f8057ba29f65b26ee8f292073edb2f441f0)), closes [#477](https://github.com/Moonofweisheng/wot-design-uni/issues/477)
* ✨ Input 组件新增clear-triger属性 ([#476](https://github.com/Moonofweisheng/wot-design-uni/issues/476)) ([364cfbf](https://github.com/Moonofweisheng/wot-design-uni/commit/364cfbf1af7a9109be9af59b543b4ccef9c32916)), closes [#462](https://github.com/Moonofweisheng/wot-design-uni/issues/462)
* ✨ Swiper 轮播组件增加value-key用于自定义目标字段属性名 ([#485](https://github.com/Moonofweisheng/wot-design-uni/issues/485)) ([f207876](https://github.com/Moonofweisheng/wot-design-uni/commit/f20787690368e341850c2fd51cf725b26b192ec9)), closes [#410](https://github.com/Moonofweisheng/wot-design-uni/issues/410)
* ✨ Textarea 组件新增clear-triger属性 ([1c13f2e](https://github.com/Moonofweisheng/wot-design-uni/commit/1c13f2e629fc259e282d7d859097f8905ef1053e)), closes [#462](https://github.com/Moonofweisheng/wot-design-uni/issues/462)
* 组件text新增金额类型，前后插槽，下划线等功能 ([#452](https://github.com/Moonofweisheng/wot-design-uni/issues/452)) ([95735be](https://github.com/Moonofweisheng/wot-design-uni/commit/95735be75e276b8679a5a76c9cbe49ea29a9b18d))
* **drop-menu:** 支持自定义图标以及before-toggle ([#479](https://github.com/Moonofweisheng/wot-design-uni/issues/479)) ([108e1b3](https://github.com/Moonofweisheng/wot-design-uni/commit/108e1b36c69cdb28b59f8742d82bb78540a0e043))

### [1.3.6](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.5...v1.3.6) (2024-07-26)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Button为disabled状态时仍能触发open-type指定事件的问题 ([a64a570](https://github.com/Moonofweisheng/wot-design-uni/commit/a64a5707d2573c042cd9bb16d6f7fecba9a38291)), closes [#458](https://github.com/Moonofweisheng/wot-design-uni/issues/458)
* 🐛 修复MessageBox设置为prompt时输入框绑定值异常的问题 ([140d960](https://github.com/Moonofweisheng/wot-design-uni/commit/140d96019d91a51f2af2efbd91a279d203a8408b))


### ✏️ Documentation | 文档

* ✏️  文档地址更新并增加QQ2群二维码 ([ef1ad01](https://github.com/Moonofweisheng/wot-design-uni/commit/ef1ad011f205612d6d2a8f5fc8cbf7d05dfffc7d))
* ✏️  修复Toast组件文档中关于提示方法描述的错误 ([8ed19a2](https://github.com/Moonofweisheng/wot-design-uni/commit/8ed19a2949064ec93cc281aadae4710d4d24a25f))
* ✏️  修正DateTimePicker文档中关于minDate和maxDate类型标注错误的问题 ([b322264](https://github.com/Moonofweisheng/wot-design-uni/commit/b322264c9a84d9acb82276ecacf5f12f1fd25f6e))

### [1.3.5](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.4...v1.3.5) (2024-07-20)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Text组件使用日期工具方法路径错误的问题 ([c4071c3](https://github.com/Moonofweisheng/wot-design-uni/commit/c4071c3759d8328f5dd6a6a374bb91dda5af1029)), closes [#453](https://github.com/Moonofweisheng/wot-design-uni/issues/453)


### ✏️ Documentation | 文档

* ✏️  文档中全局引入ElTag用于显示组件上线版本 ([1d05654](https://github.com/Moonofweisheng/wot-design-uni/commit/1d056547c89f4b6f39e9f2b503d55790abc02b52))
* ✏️  修复RadioBox文档中关于表单模式表述错误的问题 ([1da6c34](https://github.com/Moonofweisheng/wot-design-uni/commit/1da6c34565d20c7fdb0970cfc93dada208b1f82e))
* ✏️  优化Segmented分段器关于绑定激活项的文档 ([5caf3b9](https://github.com/Moonofweisheng/wot-design-uni/commit/5caf3b95073c9bf28f280cbe88431a40f937e994))

### [1.3.4](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.3...v1.3.4) (2024-07-19)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复双向滑块响应式丢失 ([#436](https://github.com/Moonofweisheng/wot-design-uni/issues/436)) ([825ea9b](https://github.com/Moonofweisheng/wot-design-uni/commit/825ea9b5b81981ae2a8cb497b412bf950caf6aaf))
* 🐛 修复Input组件[@input](https://github.com/input)事件参数错误的问题 ([82357f9](https://github.com/Moonofweisheng/wot-design-uni/commit/82357f916def6283003aef64ef522a5bb155c307))
* 🐛 修复Table组件异步填充数据源无效的问题 ([c795c00](https://github.com/Moonofweisheng/wot-design-uni/commit/c795c00560a267de41d8ef3f976fe16c6ad8ba00)), closes [#445](https://github.com/Moonofweisheng/wot-design-uni/issues/445)
* 🐛 修复Upload组件accept为media时图片预览顺序混乱的问题 ([f8c1053](https://github.com/Moonofweisheng/wot-design-uni/commit/f8c1053abd2b0ce3f3bee89fbd0e9adcdf1961d2)), closes [#442](https://github.com/Moonofweisheng/wot-design-uni/issues/442)
* 🐛 Button按钮click事件直接透传event ([017aeda](https://github.com/Moonofweisheng/wot-design-uni/commit/017aeda89d7950024baddc40a4a1e83a36010be3)), closes [#443](https://github.com/Moonofweisheng/wot-design-uni/issues/443)
* 修复 textarea 组件同时使用 auto-height 和 no-border 属性时，no-border 属性不生效 ([#448](https://github.com/Moonofweisheng/wot-design-uni/issues/448)) ([a2f4b32](https://github.com/Moonofweisheng/wot-design-uni/commit/a2f4b322f5b9bd01a36a46b904684d531d5e6730))


### ✨ Features | 新功能

* ✨ Button按钮组件支持使用自定义组件 ([517b583](https://github.com/Moonofweisheng/wot-design-uni/commit/517b583f262e374154111d635644dfb225a473c9)), closes [#310](https://github.com/Moonofweisheng/wot-design-uni/issues/310)
* ✨ Toast 轻提示组件支持使用组件库内置和自定义图标 ([723c51b](https://github.com/Moonofweisheng/wot-design-uni/commit/723c51bac98bb751af44f13737fba12deb298dd4)), closes [#444](https://github.com/Moonofweisheng/wot-design-uni/issues/444)
* ✨ Upload上传组件新增支持successStatus属性 ([99eab74](https://github.com/Moonofweisheng/wot-design-uni/commit/99eab74260e1e3c43f9f4b147edb4a5a3147d086))
* 新增Text 文本组件 ([#403](https://github.com/Moonofweisheng/wot-design-uni/issues/403)) ([14f5f44](https://github.com/Moonofweisheng/wot-design-uni/commit/14f5f4430a7cd599149adf16f7bc704dc42f4d90))


### ✏️ Documentation | 文档

* ✏️  调整QQ群与提问相关文档 ([cb11e98](https://github.com/Moonofweisheng/wot-design-uni/commit/cb11e9822ff714d3c1e03ad5f2b01b0a07e8fcec))
* ✏️  移除示例demo中手机号等字样方便过审 ([7488a7f](https://github.com/Moonofweisheng/wot-design-uni/commit/7488a7f708035b5f59c60078190c880cc8d11800))

### [1.3.3](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.2...v1.3.3) (2024-07-14)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复IndexBar索引值显示错误的问题 ([#433](https://github.com/Moonofweisheng/wot-design-uni/issues/433)) ([19dc35b](https://github.com/Moonofweisheng/wot-design-uni/commit/19dc35bf40eecc263ed19e9f54d05c004b1d3425)), closes [#408](https://github.com/Moonofweisheng/wot-design-uni/issues/408)


### ✏️ Documentation | 文档

* ✏️  调整演示demo中图片到npmmirror上 ([93ff5f9](https://github.com/Moonofweisheng/wot-design-uni/commit/93ff5f938fdb1ced622080bce9168bfe0e7ed771))
* ✏️  新增关于messageBox弹出多个的常见问题解答 ([a362928](https://github.com/Moonofweisheng/wot-design-uni/commit/a3629283aa3838803df900512afc990f920b3e0b))
* ✏️  修复SelectPicker文档中存在的拼写错误 ([9e28b57](https://github.com/Moonofweisheng/wot-design-uni/commit/9e28b5771a30fa0bf8aad60e8e494e0bc976d9a1)), closes [#426](https://github.com/Moonofweisheng/wot-design-uni/issues/426)
* ✏️  Upload文档增加微信隐私协议的介绍 ([c7f3a4a](https://github.com/Moonofweisheng/wot-design-uni/commit/c7f3a4adc2907bab30f5e075417f34541cba7a5e))

### [1.3.2](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.1...v1.3.2) (2024-07-08)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复ActionSheet样式调整导致微信编译错误的问题 ([a9189d2](https://github.com/Moonofweisheng/wot-design-uni/commit/a9189d2c263459a33cdbb68bec3dd0dd0213b5c0))

### [1.3.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.3.0...v1.3.1) (2024-07-08)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复暗黑模式下Grid和ActionSheet组件部分样式异常的问题 ([a28938c](https://github.com/Moonofweisheng/wot-design-uni/commit/a28938c91d4b437e6b583793e32f8373cec102ae)), closes [#409](https://github.com/Moonofweisheng/wot-design-uni/issues/409)

## [1.3.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.28...v1.3.0) (2024-07-07)


### ✨ Features | 新功能

* ✨ Upload上传组件新增支持上传视频和文件 ([#412](https://github.com/Moonofweisheng/wot-design-uni/issues/412)) ([e07dbdd](https://github.com/Moonofweisheng/wot-design-uni/commit/e07dbdd5305c112fe3648ce988a45b2cc36ae143)), closes [#186](https://github.com/Moonofweisheng/wot-design-uni/issues/186) [#336](https://github.com/Moonofweisheng/wot-design-uni/issues/336)
* ✨select-picker组件增加open、close事件 ([#395](https://github.com/Moonofweisheng/wot-design-uni/issues/395)) ([9237a04](https://github.com/Moonofweisheng/wot-design-uni/commit/9237a04bcbde9960864b9a7b09a64fc2b6c27595))


### ✏️ Documentation | 文档

* ✏️  调整ColPicker多列选择器文档中省市区数据源及演示demo ([d09bd03](https://github.com/Moonofweisheng/wot-design-uni/commit/d09bd037e735b02264074c2a251c59c01b8ff571))
* ✏️  DropDownItem文档增加closed和opened介绍 ([401bd28](https://github.com/Moonofweisheng/wot-design-uni/commit/401bd284ceaafe957a0f4184d0a009bed70e9377))
* ✏️ PasswordInput 修复示例代码错误的问题 ([#391](https://github.com/Moonofweisheng/wot-design-uni/issues/391)) ([519d172](https://github.com/Moonofweisheng/wot-design-uni/commit/519d17235b7c61acf2048104a495690bff0b9804))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Calendar等组件暗黑模式部分样式异常的问题 ([281e20f](https://github.com/Moonofweisheng/wot-design-uni/commit/281e20f2a922d98c00321d1316efc96b985c620d)), closes [#388](https://github.com/Moonofweisheng/wot-design-uni/issues/388)
* 🐛 修复IndexBar点击索引序号时未显示预期索引值的问题 ([c33991e](https://github.com/Moonofweisheng/wot-design-uni/commit/c33991ee14b8108bcd084b5d7b59f35cb79b2b35)), closes [#408](https://github.com/Moonofweisheng/wot-design-uni/issues/408)
* 🐛 修复Swiper在微信端长时间处于后台出现抖动的问题 ([#413](https://github.com/Moonofweisheng/wot-design-uni/issues/413)) ([4741439](https://github.com/Moonofweisheng/wot-design-uni/commit/4741439277f1a2668634a4e5e3649236ed95a627)), closes [#411](https://github.com/Moonofweisheng/wot-design-uni/issues/411)

### [1.2.28](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.27...v1.2.28) (2024-06-24)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复omitBy工具方法实现错误导致Tabbar等组件徽标显示异常的问题 ([1491fe4](https://github.com/Moonofweisheng/wot-design-uni/commit/1491fe44616a0db1c165e2bed29637f8c46fbc7e))

### [1.2.27](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.26...v1.2.27) (2024-06-21)


### ✨ Features | 新功能

* ✨ add title slot for wd-collapse-item ([#372](https://github.com/Moonofweisheng/wot-design-uni/issues/372)) ([0252bd9](https://github.com/Moonofweisheng/wot-design-uni/commit/0252bd98254f8e108e545651127a744640b39692)), closes [#356](https://github.com/Moonofweisheng/wot-design-uni/issues/356)


### ✏️ Documentation | 文档

* ✏️  调整文档中关于类型声明文件的配置 ([0c38e98](https://github.com/Moonofweisheng/wot-design-uni/commit/0c38e986f0151b8aa2e17ab1770d1f39b178d354))
* ✏️  Input 密码输入框示例移除disabled属性 ([3026c78](https://github.com/Moonofweisheng/wot-design-uni/commit/3026c78d237217b08e2fa3cdf64260c294a61b2b))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复ActionSheet禁用和加载状态时仍有点击效果的问题 ([c6baf45](https://github.com/Moonofweisheng/wot-design-uni/commit/c6baf452f0b626dea378148131d624589bb0c47e)), closes [#379](https://github.com/Moonofweisheng/wot-design-uni/issues/379)
* 🐛 修复Sidebar等组件css变量前缀错误的问题 ([0c31e16](https://github.com/Moonofweisheng/wot-design-uni/commit/0c31e16699e0b70b91384da0a5c0537b791e6bcf))

### [1.2.26](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.25...v1.2.26) (2024-06-14)


### ✨ Features | 新功能

* ✨ fab组件添加gap属性 ([#366](https://github.com/Moonofweisheng/wot-design-uni/issues/366)) ([7b44765](https://github.com/Moonofweisheng/wot-design-uni/commit/7b44765adc08fd16e055fbd326698a8f6b708426))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Fab初始化时存在初始位置闪现的问题 ([74c90be](https://github.com/Moonofweisheng/wot-design-uni/commit/74c90beb402e519fee1057870b0631673945cb73))
* 🐛 修复Transition被打断时出现显示异常的问题 ([#368](https://github.com/Moonofweisheng/wot-design-uni/issues/368)) ([9c21b95](https://github.com/Moonofweisheng/wot-design-uni/commit/9c21b9512076cc95098a36ee7a7283f70386c94b))

### [1.2.25](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.24...v1.2.25) (2024-06-09)


### ✨ Features | 新功能

* ✨ Pickerview选择器新增immediate-change属性，目前微信和支付宝小程序支持。 ([3428ae1](https://github.com/Moonofweisheng/wot-design-uni/commit/3428ae17889a36552010e3f7cc4a9bebb7a94461))
* ✨ Slider暴露initSlider方法用于外部初始化slider宽度信息 ([fc3e4ef](https://github.com/Moonofweisheng/wot-design-uni/commit/fc3e4ef3bb108e6bb6a660ffead40f4658c119e7)), closes [#344](https://github.com/Moonofweisheng/wot-design-uni/issues/344)


### ✏️ Documentation | 文档

* ✏️  常见问题中增加关于交流群的内容 ([9ddaeec](https://github.com/Moonofweisheng/wot-design-uni/commit/9ddaeec32299463406ae2f60406cf39daff575cf))
* ✏️  更新文档中组件数量 ([10d2ba9](https://github.com/Moonofweisheng/wot-design-uni/commit/10d2ba9035970c5627fc731219ec529e634d2578))
* ✏️  增加关于微信小程序v-if和slot执行顺序异常问题的介绍 ([6a14879](https://github.com/Moonofweisheng/wot-design-uni/commit/6a1487900b214d071fdbda52034f220b8b046eec))
* ✏️ 修正upload组件文档中change事件的dmeo错误 ([#360](https://github.com/Moonofweisheng/wot-design-uni/issues/360)) ([61004d3](https://github.com/Moonofweisheng/wot-design-uni/commit/61004d30ed3e79b439ef8a3c5d2e78723d7fa017))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复时间选择器设置minDate之后选择器显示值和实际选择值不一致的问题 ([3c0284f](https://github.com/Moonofweisheng/wot-design-uni/commit/3c0284f1f27b743ea3bdb7eeac5c489939057e13)), closes [#339](https://github.com/Moonofweisheng/wot-design-uni/issues/339)
* 🐛 修复ImgCropper未暴露resetImg和setRoate方法的问题 ([e58f111](https://github.com/Moonofweisheng/wot-design-uni/commit/e58f1111f2ae8e2da23e60c0ed60130373117970)), closes [#354](https://github.com/Moonofweisheng/wot-design-uni/issues/354)
* 🐛 修复Tag在钉钉小程序平台close方法不执行的问题 ([242d2f2](https://github.com/Moonofweisheng/wot-design-uni/commit/242d2f25c6ac829b5d20d63d520b1f8c8ae921a8)), closes [#359](https://github.com/Moonofweisheng/wot-design-uni/issues/359)

### [1.2.24](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.23...v1.2.24) (2024-06-03)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Input支付宝小程序number/digit类型使用maxlength=-1时v-model失效的问题 ([7aa21b0](https://github.com/Moonofweisheng/wot-design-uni/commit/7aa21b0baadeccf4f0eb179f74332013acec6a10))

### [1.2.23](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.22...v1.2.23) (2024-06-02)


### ✏️ Documentation | 文档

* ✏️  更新LICENSE文件地址 ([dae3ffc](https://github.com/Moonofweisheng/wot-design-uni/commit/dae3ffcc7b488dd6d87bc1c5e8ae16a78b794f85))
* ✏️  修复loading组件示例代码中文标点符号、img-cropper组件示例代码标签缺失的问题 ([#347](https://github.com/Moonofweisheng/wot-design-uni/issues/347)) ([d171255](https://github.com/Moonofweisheng/wot-design-uni/commit/d171255899d232f141c84aa1251c03cc1d0a0b75))
* update select-picker doc ([#346](https://github.com/Moonofweisheng/wot-design-uni/issues/346)) ([7454452](https://github.com/Moonofweisheng/wot-design-uni/commit/7454452ad44331f329408cead9b483cf774cce5f))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Cell的value为0时无法渲染的问题 ([dc64c09](https://github.com/Moonofweisheng/wot-design-uni/commit/dc64c09e6c6f991fe6f8f9fcfa309392e233ce21))
* 🐛 修复Sticky在h5和App端缓慢拖动时存在几率始终固定在顶部的问题 ([#350](https://github.com/Moonofweisheng/wot-design-uni/issues/350)) ([c5b3c5f](https://github.com/Moonofweisheng/wot-design-uni/commit/c5b3c5f68e1bb376249ed5f2c30b1898cc375abe))


### ✨ Features | 新功能

* ✨ 调整Circle环形进度条在微信小程序端使用canvas2d支持同层渲染 ([#351](https://github.com/Moonofweisheng/wot-design-uni/issues/351)) ([4489517](https://github.com/Moonofweisheng/wot-design-uni/commit/44895179e4688ceb995ea1968d23df4f0bf9cdc2))

### [1.2.22](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.21...v1.2.22) (2024-05-23)


### ✏️ Documentation | 文档

* ✏️  组件库介绍组件数量调整至70+ ([efd55ca](https://github.com/Moonofweisheng/wot-design-uni/commit/efd55ca8afc07368b8d9f348d73c9a25cf186c47))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Form指定prop为`a.b`时校验失败的问题 ([#329](https://github.com/Moonofweisheng/wot-design-uni/issues/329)) ([ab600b9](https://github.com/Moonofweisheng/wot-design-uni/commit/ab600b915a647ff089d5cfbb0ac955e3baa581c3))

### [1.2.21](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.20...v1.2.21) (2024-05-20)


### ✨ Features | 新功能

* ✨ 添加索引栏组件 ([#321](https://github.com/Moonofweisheng/wot-design-uni/issues/321)) ([f84e9af](https://github.com/Moonofweisheng/wot-design-uni/commit/f84e9affb1a044a37661c5d1dc118d834b49239c))
* ✨ img组件添加loading、error插槽 ([#323](https://github.com/Moonofweisheng/wot-design-uni/issues/323)) ([00ffa9f](https://github.com/Moonofweisheng/wot-design-uni/commit/00ffa9f3e54f3ba9eec967887e195c0266ef0a41))
* ✨ skeleton添加默认内容插槽 ([#322](https://github.com/Moonofweisheng/wot-design-uni/issues/322)) ([9a68c47](https://github.com/Moonofweisheng/wot-design-uni/commit/9a68c477dbf5e0f30f74882df92251eac707fdde))
* add backtop ([#314](https://github.com/Moonofweisheng/wot-design-uni/issues/314)) ([bf9e55a](https://github.com/Moonofweisheng/wot-design-uni/commit/bf9e55a24e676a764b1e035ca86bd6fe26b87420))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Cell组件单独使用时设置border无效的问题 ([19b9f19](https://github.com/Moonofweisheng/wot-design-uni/commit/19b9f196466b88456e8b3e7221afd710da3df99b))
* 🐛 修复Picker选择器多列选择模式绑定值为空数组时将列第一项作为显示值的问题 ([c3b96ce](https://github.com/Moonofweisheng/wot-design-uni/commit/c3b96ce912bfc13d30c09393dd57cbcbf2d0b80a))
* 🐛 修复Sticky吸顶组件在微信小程序中放置在页面顶部时吸顶失效的问题 ([4ca3ebe](https://github.com/Moonofweisheng/wot-design-uni/commit/4ca3ebe7202f118a1bf6a462b27dc76ab6c720f9)), closes [#325](https://github.com/Moonofweisheng/wot-design-uni/issues/325)
* 修复wd-button在自定义样式时激活态样式问题 ([#312](https://github.com/Moonofweisheng/wot-design-uni/issues/312)) ([7544d69](https://github.com/Moonofweisheng/wot-design-uni/commit/7544d690f176b44a8e016688723af70d2a77388a))


### ✏️ Documentation | 文档

* ✏️  常见问题增加关于useToast等hooks的答疑以及自定义编译平台的内容 ([65597c7](https://github.com/Moonofweisheng/wot-design-uni/commit/65597c76b812e5e61be17879de320989d0873df4))
* ✏️  文档中增加解释导入方式的faq ([83fa0b0](https://github.com/Moonofweisheng/wot-design-uni/commit/83fa0b06927fddabd1021ee02c3c7f8377704786))

### [1.2.20](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.19...v1.2.20) (2024-05-12)


### ✏️ Documentation | 文档

* ✏️  调整捐赠榜单和友情链接的数据到cloudflare上 ([1314373](https://github.com/Moonofweisheng/wot-design-uni/commit/1314373af1dff95aee4cd3cddefebcb3a6a5c5d2))
* ✏️  推荐文档地址调整至cloudflare ([9119f30](https://github.com/Moonofweisheng/wot-design-uni/commit/9119f30b23676ad1b9a3869e362a3840ba288dc3))
* ✏️  Curtain组件width属性标记为number类型 ([d172ce7](https://github.com/Moonofweisheng/wot-design-uni/commit/d172ce7ffcfebf29b184b57a7f74a25acad1f967)), closes [#303](https://github.com/Moonofweisheng/wot-design-uni/issues/303)


### ✨ Features | 新功能

* Segmented组件添加click事件 ([#301](https://github.com/Moonofweisheng/wot-design-uni/issues/301)) ([225ce22](https://github.com/Moonofweisheng/wot-design-uni/commit/225ce225ac7faedffd4686b908076c47ba111695))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复MessageBox中使用TextArea字数统计样式错误的问题 ([b186ac0](https://github.com/Moonofweisheng/wot-design-uni/commit/b186ac09294a4fede16c514cd96ff94f661fcbc9)), closes [#290](https://github.com/Moonofweisheng/wot-design-uni/issues/290)
* 🐛 修复PickerView可以滚动到禁用选项的问题 ([edd44ed](https://github.com/Moonofweisheng/wot-design-uni/commit/edd44eda3fc9879dc980b02d1885d77530a3d2e8)), closes [#302](https://github.com/Moonofweisheng/wot-design-uni/issues/302)
* 🐛 修复Popover指定placement后箭头显示异常的问题([#306](https://github.com/Moonofweisheng/wot-design-uni/issues/306)) ([a9b108d](https://github.com/Moonofweisheng/wot-design-uni/commit/a9b108d231a8e537aab4ba49781590bcafb9354d))
* 🐛 修复release脚本处理最低版本号问题 ([c8077c9](https://github.com/Moonofweisheng/wot-design-uni/commit/c8077c9f3790e7844c902528d43596c4bc4025af))
* 🐛 修复Sticky组件获取节点错误的问题 ([5483ea6](https://github.com/Moonofweisheng/wot-design-uni/commit/5483ea6c447f85229af66b950e34c1dc690fadb1))
* 修复wd-calendar未抛出事件 ([c1203c9](https://github.com/Moonofweisheng/wot-design-uni/commit/c1203c91841c3d5bcd407923a577711e462207a3))

### [1.2.19](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.18...v1.2.19) (2024-05-01)


### ✨ Features | 新功能

* ✨ `Slider`组件`min`值允许负数 ([9e7c8d3](https://github.com/Moonofweisheng/wot-design-uni/commit/9e7c8d33eb2c0fccc44f465ed3b28d2cd81efa2d)), closes [#266](https://github.com/Moonofweisheng/wot-design-uni/issues/266)
* ✨ fab添加draggable属性 ([#259](https://github.com/Moonofweisheng/wot-design-uni/issues/259)) ([5e0cd6c](https://github.com/Moonofweisheng/wot-design-uni/commit/5e0cd6caa2296a44a3dc1b3d33033d4cc91d01b9))
* ✨ table组件添加index参数 ([2161705](https://github.com/Moonofweisheng/wot-design-uni/commit/2161705a2f868f874b89ee28b6029677854a7741))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复`slider`组件`step`属性无效问题 ([50133b9](https://github.com/Moonofweisheng/wot-design-uni/commit/50133b9e5e2a18aace8cbf9eb7570579cc563f1d)), closes [#269](https://github.com/Moonofweisheng/wot-design-uni/issues/269)
* 🐛 修复Cell组件title文字对齐方式受外部影响的问题 ([caf66b6](https://github.com/Moonofweisheng/wot-design-uni/commit/caf66b6beeb236a9295cf0d8ec250557dc19e54e)), closes [#282](https://github.com/Moonofweisheng/wot-design-uni/issues/282)
* 🐛 修复SelectPicker无默认值时仍会查找选择项节点的问题 ([130c438](https://github.com/Moonofweisheng/wot-design-uni/commit/130c4383dc862e190b96bc3670a852a4cce3e629)), closes [#281](https://github.com/Moonofweisheng/wot-design-uni/issues/281)
* 🐛 修复Slider组件max和min变化时滑块和进度条未更新的问题 ([#276](https://github.com/Moonofweisheng/wot-design-uni/issues/276)) ([69303b5](https://github.com/Moonofweisheng/wot-design-uni/commit/69303b52077bd268e3563fa9096e91530bb978c5))


### ✏️ Documentation | 文档

* ✏️  补充Table组件关于Events的介绍 ([6760317](https://github.com/Moonofweisheng/wot-design-uni/commit/676031781b189a3a89dba9e57cb989c06999091a)), closes [#260](https://github.com/Moonofweisheng/wot-design-uni/issues/260)

### [1.2.18](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.17...v1.2.18) (2024-04-23)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复InputNumber步进器组件在初始化时未发生变化仍触发change的问题 ([6ac20fd](https://github.com/Moonofweisheng/wot-design-uni/commit/6ac20fd60728e9056282466571c64ff747af4c9f))
* 🐛 修复InputNumber的change事件无法取到当前绑定值的更新的问题 ([45980c5](https://github.com/Moonofweisheng/wot-design-uni/commit/45980c526a0b47fb1cb2c95ad50ab9f226464d7c))

### [1.2.17](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.16...v1.2.17) (2024-04-18)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复debounce调整后导致tabs无法切换的问题 ([bd594a7](https://github.com/Moonofweisheng/wot-design-uni/commit/bd594a7202d3cd4e641f23a0b98d468227370d6f))

### [1.2.16](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.15...v1.2.16) (2024-04-17)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Gap组件文档中safeAreaBottom属性名错误的问题 ([b03e39f](https://github.com/Moonofweisheng/wot-design-uni/commit/b03e39f601a68ff5f7e72aa3605279683e5e5b74))
* 🐛 修复PickerView在绑定值为0时无法生效的问题 ([742b4d6](https://github.com/Moonofweisheng/wot-design-uni/commit/742b4d65255926a0e8c3899ae3ee2eb9d7b7e5e5))


### ✏️ Documentation | 文档

* ✏️  补充Img图片支持图片预览的最低支持版本 ([82d8e49](https://github.com/Moonofweisheng/wot-design-uni/commit/82d8e493c0723da9e3ba88981a45ea31ae8ef9d5)), closes [#244](https://github.com/Moonofweisheng/wot-design-uni/issues/244)


### ✨ Features | 新功能

* ✨ 阻止tag的关闭和新增事件冒泡 ([4c22cf5](https://github.com/Moonofweisheng/wot-design-uni/commit/4c22cf515e2e121b3bbe7d9fd64c5777ce72df32))
* ✨ Checkbox 新增toggle方法 ([6ec0397](https://github.com/Moonofweisheng/wot-design-uni/commit/6ec0397bc9a5e80780b22245c29c6f05f1b25313)), closes [#239](https://github.com/Moonofweisheng/wot-design-uni/issues/239)
* ✨ date-time-picker-view 添加年选择 ([#241](https://github.com/Moonofweisheng/wot-design-uni/issues/241)) ([0073b32](https://github.com/Moonofweisheng/wot-design-uni/commit/0073b323967fe859ed15284607f4117243614d5e))
* ✨ Noticebar 通知栏新增支持click点击事件 ([6fc786d](https://github.com/Moonofweisheng/wot-design-uni/commit/6fc786d9be63c2f021c10e0c48c466e0114f5c24))
* ✨ TableColumn组件value插槽新增index参数 ([b8b03c3](https://github.com/Moonofweisheng/wot-design-uni/commit/b8b03c352b62e31a584faa41ced65d72bc427d89))

### [1.2.15](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.14...v1.2.15) (2024-04-11)


### ✏️ Documentation | 文档

* ✏️ 优化文档及演示demo访问速度 ([#232](https://github.com/Moonofweisheng/wot-design-uni/issues/232)) ([8d3f617](https://github.com/Moonofweisheng/wot-design-uni/commit/8d3f617e403fdbf53a29ca0d48624dc5da74fc94))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Button不同尺寸显示固定尺寸icon样式不协调的问题 ([2a73992](https://github.com/Moonofweisheng/wot-design-uni/commit/2a73992d9c551c51f4f3672167b972d0d0cca881)), closes [#235](https://github.com/Moonofweisheng/wot-design-uni/issues/235)
* 🐛 修复Upload自定义上传样式时未应用limit数量限制和customEvokeClass的问题 ([50baac8](https://github.com/Moonofweisheng/wot-design-uni/commit/50baac876279029214062a23072bb7c309110c0c))


### ✨ Features | 新功能

* ✨ MessageBox支持确认前置处理钩子beforeConfirm ([78bed6a](https://github.com/Moonofweisheng/wot-design-uni/commit/78bed6a212a12639cd8831e3a1ad4542d6fbf518)), closes [#229](https://github.com/Moonofweisheng/wot-design-uni/issues/229)
* ✨ Toast轻提示增加opened、closed两个钩子 ([ead218b](https://github.com/Moonofweisheng/wot-design-uni/commit/ead218b87b6b0bd53c8c541c38561fdb45396c72))

### [1.2.14](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.13...v1.2.14) (2024-04-09)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复NoticeBar通知栏在小程序端垂直滚动无效的问题 ([eaeb935](https://github.com/Moonofweisheng/wot-design-uni/commit/eaeb935a299dee3c5e54dee6b926d625a7ef66a8))

### [1.2.13](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.12...v1.2.13) (2024-04-08)


### ✏️ Documentation | 文档

* ✏️  demo首页使用Cell组件重构显示样式 ([9ae8a20](https://github.com/Moonofweisheng/wot-design-uni/commit/9ae8a207f3ee8c765e6269b7ef5cb42ff1cd10fd))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复升级vue到3.4.2.之后defineEmits位置不规范导致访问'emit'报错的问题 ([ef574cf](https://github.com/Moonofweisheng/wot-design-uni/commit/ef574cfcb8f805ec308d8364cb887b65394e6108)), closes [#226](https://github.com/Moonofweisheng/wot-design-uni/issues/226)
* 🐛 修复NumberKeyboard暗黑模式无效的问题 ([b0cc958](https://github.com/Moonofweisheng/wot-design-uni/commit/b0cc958e0fd762915fe427275288a87d0c58ed38))
* 🐛 修复Upload组件sourceType属性丢失默认值导致在iOS系统微信小程序报错的问题 ([b70991a](https://github.com/Moonofweisheng/wot-design-uni/commit/b70991a1133be77439fc139df9019527ded9b71d)), closes [#227](https://github.com/Moonofweisheng/wot-design-uni/issues/227)

### [1.2.12](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.11...v1.2.12) (2024-04-07)


### ✨ Features | 新功能

* ✨ 增强`notice-bar`组件`vertical`模式下插槽的功能 ([e3daed5](https://github.com/Moonofweisheng/wot-design-uni/commit/e3daed516c958dbb5bff0d4a51d7d16cf9971da1))
* ✨ NumberKeyboard数字键盘增加名为title的插槽 ([7edc746](https://github.com/Moonofweisheng/wot-design-uni/commit/7edc7462aa43ea800536ac0a464bb6e6c131a20c)), closes [#213](https://github.com/Moonofweisheng/wot-design-uni/issues/213)
* ✨ StatusTip缺省提示组件新增支持图片mode和自定义图片宽高 ([171c2bb](https://github.com/Moonofweisheng/wot-design-uni/commit/171c2bbdaf9e5ec62fa13fad1079b86daca85d54))
* ✨ Swiper轮播为image-mode增加ts类型标注 ([9a4ceb2](https://github.com/Moonofweisheng/wot-design-uni/commit/9a4ceb21160dab06a08b39c35e50f69c7c49d023))


### 🐛 Bug Fixes | Bug 修复

* 修复 step 的 description 插槽不显示的bug ([f979ccc](https://github.com/Moonofweisheng/wot-design-uni/commit/f979cccc05b9956fe0df121fc2e8cbbb24544766))


### ✏️ Documentation | 文档

* ✏️  调整演示demo iframe加载完成后再同步当前是否暗黑模式 ([9ab370e](https://github.com/Moonofweisheng/wot-design-uni/commit/9ab370e19395aaef013da8a386dd310c7deec59e)), closes [#210](https://github.com/Moonofweisheng/wot-design-uni/issues/210)
* ✏️  Steps步骤条增加控制进度的示例 ([8025174](https://github.com/Moonofweisheng/wot-design-uni/commit/80251742f6f5d560d546bd7cd812c35433158a91))

### [1.2.11](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.10...v1.2.11) (2024-04-06)


### ⚡ Performance Improvements | 性能优化

* ⚡ 优化Calendar日历选择器打开关闭时的动画效果 ([5fb9542](https://github.com/Moonofweisheng/wot-design-uni/commit/5fb9542087c62b8cf6974d2e5f4b5f455b456495))


### ✏️ Documentation | 文档

* ✏️  `notice-bar`组件补充`event`事件 ([8f11c8a](https://github.com/Moonofweisheng/wot-design-uni/commit/8f11c8a0fe02f3bb0a2e7a67e105f2e33d658477))
* ✏️  优化文档与演示demo同步暗黑模式的逻辑 ([d057607](https://github.com/Moonofweisheng/wot-design-uni/commit/d0576071ebaeb69828b8c5e789471c3324dabb1f)), closes [#210](https://github.com/Moonofweisheng/wot-design-uni/issues/210)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复ActionSheet丢失props默认值的问题 ([e50cccc](https://github.com/Moonofweisheng/wot-design-uni/commit/e50cccceeccebeb99ca69712b9f64e8a9e5fd23f))
* 🐛 修复Calendar部分文字运行时切换国际化未应用国际化文字的问题 ([857c922](https://github.com/Moonofweisheng/wot-design-uni/commit/857c922d4f3542cb543b8b99600bf9c48ffa24b9))
* 🐛 修复Calendar的type为datetime且无默认值时无法在首次选择日期后选择时间的问题 ([3746ba3](https://github.com/Moonofweisheng/wot-design-uni/commit/3746ba304021c56e14414dfaed225c465869a473)), closes [#214](https://github.com/Moonofweisheng/wot-design-uni/issues/214)
* 🐛 修复search组件suffix插槽无法使用的问题 ([6a4af41](https://github.com/Moonofweisheng/wot-design-uni/commit/6a4af41b93e8edd92e297eaea7c1bc566bfb4d6d))


### ✨ Features | 新功能

* ✨ `notice-bar`增加垂直滚动功能 ([93d048b](https://github.com/Moonofweisheng/wot-design-uni/commit/93d048b219d66718880609d6ec9086ce058121fa)), closes [#122](https://github.com/Moonofweisheng/wot-design-uni/issues/122)
* ✨ `notice-bar`增加多文本轮播功能 ([c2e5e9b](https://github.com/Moonofweisheng/wot-design-uni/commit/c2e5e9bbd86093a5795d93aa23c2bd03d8416767))
* ✨ `upload`组件增加`mode`属性传递给预览`image` ([acf3f99](https://github.com/Moonofweisheng/wot-design-uni/commit/acf3f999033df7b2925040ff83ad1088d0e59a44)), closes [#203](https://github.com/Moonofweisheng/wot-design-uni/issues/203)
* ✨ img组件的mode属性增加类型`ImageMode` ([1398982](https://github.com/Moonofweisheng/wot-design-uni/commit/139898209fea4559a69cbaffed2e7b45b91aa107))
* ✨ img组件增加enable-preview属性来支持点击预览功能 ([e6a3b73](https://github.com/Moonofweisheng/wot-design-uni/commit/e6a3b73142e37547e60b8f1dd1845fc3e9d5d17e)), closes [#193](https://github.com/Moonofweisheng/wot-design-uni/issues/193)
* ✨ swiper组件增加current属性控制轮播项功能 ([443fd8a](https://github.com/Moonofweisheng/wot-design-uni/commit/443fd8a19e3b73ee61ae6a1692c5409e3d0c0b84)), closes [#211](https://github.com/Moonofweisheng/wot-design-uni/issues/211)

### [1.2.10](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.9...v1.2.10) (2024-04-02)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Step组件description插槽无法使用的问题 ([e431294](https://github.com/Moonofweisheng/wot-design-uni/commit/e431294ea2b97858f93513c112f1a7574654a87d))
* 🐛 修复Toast、MessageBox二次打开时部分参数未重置的问题 ([e2e7d37](https://github.com/Moonofweisheng/wot-design-uni/commit/e2e7d378e577d3d1f5582464bdd9e6c4e6d82e3d))


### ✨ Features | 新功能

* ✨ 为所有组件补充customStyle属性 ([964240e](https://github.com/Moonofweisheng/wot-design-uni/commit/964240e2e7f640586a18bec1c11b0359afcf99e2)), closes [#212](https://github.com/Moonofweisheng/wot-design-uni/issues/212)


### ✏️ Documentation | 文档

* ✏️  更新vitepress至1.0.1 ([da7684f](https://github.com/Moonofweisheng/wot-design-uni/commit/da7684f33ada867a9ba2f22bb1489c804a7d840e))
* ✏️  修复更新vitepress至1.0.1后友情链接丢失logo的问题 ([1e36c39](https://github.com/Moonofweisheng/wot-design-uni/commit/1e36c39ff7ab814741792ee91a2ee7d9085862f9))
* ✏️  Steps文档标题调整为步骤条 ([1ea7996](https://github.com/Moonofweisheng/wot-design-uni/commit/1ea79960fd9facef7aa5ef17f0f9bde03daa0d7c))

### [1.2.9](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.8...v1.2.9) (2024-04-01)


### ✨ Features | 新功能

* ✨ Segmented分段器change事件参数类型扩充为SegmentedOption ([5592bc4](https://github.com/Moonofweisheng/wot-design-uni/commit/5592bc49b9439ef34306741f16c4b5c702d562b2)), closes [#204](https://github.com/Moonofweisheng/wot-design-uni/issues/204)

### [1.2.8](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.7...v1.2.8) (2024-03-30)


### 🐛 Bug Fixes | Bug 修复

* 修复useLocale 不传 message 会丢失原有语言配置的问题 ([#209](https://github.com/Moonofweisheng/wot-design-uni/issues/209)) ([e854d0e](https://github.com/Moonofweisheng/wot-design-uni/commit/e854d0e758e44cba9213992794974b7ae5985abf))


### ✨ Features | 新功能

* ✨ SelectPicker单选模式支持自动完成 ([#207](https://github.com/Moonofweisheng/wot-design-uni/issues/207)) ([11aa887](https://github.com/Moonofweisheng/wot-design-uni/commit/11aa887ae36153c0a6a0cd9053fc94c70acbcb0d)), closes [#206](https://github.com/Moonofweisheng/wot-design-uni/issues/206)


### ✏️ Documentation | 文档

* ✏️  文档中增加@uni-helper/vite-plugin-uni-components的使用提示 ([37d9d46](https://github.com/Moonofweisheng/wot-design-uni/commit/37d9d4631e434792b1e97dccac8a52a6d253d616))

### [1.2.7](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.6...v1.2.7) (2024-03-29)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复DatePicker占位符展示缺失的问题 ([8fbca94](https://github.com/Moonofweisheng/wot-design-uni/commit/8fbca94e3690e8b86ce59fbe668805839c50192b))
* 🐛 修复DateTimePicker区域选择极值计算错误的问题 ([a1e9530](https://github.com/Moonofweisheng/wot-design-uni/commit/a1e9530a917981ca4cf96dde1aa268eaf3da059f))
* 🐛 修复picker-view初始化报错的问题 ([2102f38](https://github.com/Moonofweisheng/wot-design-uni/commit/2102f38a107a7cc8a316cb7fdc5a9738a5bb7499)), closes [#205](https://github.com/Moonofweisheng/wot-design-uni/issues/205)
* 🐛 修复picker选择器在APP端包装二维数组逻辑错误导致无法实现多列选择器的问题 ([ed5d7ac](https://github.com/Moonofweisheng/wot-design-uni/commit/ed5d7ace2050f5ae7541c8fd2ec395de56fa73cc))
* 🐛 移除文件移动时volar变更文件路径错误导致.vue文件中多出的无用路径字符串 ([1f8f8ae](https://github.com/Moonofweisheng/wot-design-uni/commit/1f8f8ae2bfb2b13a6b6fcd6435a2d75f47d0e775))

### [1.2.6](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.5...v1.2.6) (2024-03-27)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Picker 未选择时显示第一列值的问题 ([21839e0](https://github.com/Moonofweisheng/wot-design-uni/commit/21839e0caf170248f454d80645bc3aa2f3ae2cdb))
* 🐛 修复DatetimePicker区间模式未选值时显示占位符错误的问题 ([37350ee](https://github.com/Moonofweisheng/wot-design-uni/commit/37350ee5234a0f30debd22d33fb168415ec4e72d))

### [1.2.5](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.4...v1.2.5) (2024-03-24)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复textarea在支付宝小程序上清除按钮点击热区失效的问题 ([84662f2](https://github.com/Moonofweisheng/wot-design-uni/commit/84662f254b85d3b554113c9ba4f73ca695596fe9))

### [1.2.4](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.3...v1.2.4) (2024-03-23)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复textarea、input的label存在icon时高度异常的问题 ([e487ced](https://github.com/Moonofweisheng/wot-design-uni/commit/e487ced1624ce9ba35b29537481302e07050612c))
* 🐛 修复textarea无法换行的问题 ([25bd564](https://github.com/Moonofweisheng/wot-design-uni/commit/25bd56485508ae8b3dfffd13d47c306fbf42d65e))


### ✏️ Documentation | 文档

* ✏️  调整组件库基于vite配置自动引入组件的介绍 ([2d23607](https://github.com/Moonofweisheng/wot-design-uni/commit/2d2360706fbe2a5d4024ceebd82720014c6b1ea2))
* ✏️  文档推荐用户从wot-design-uni.cn迁移至wot-design-uni.gitee.io ([72a567c](https://github.com/Moonofweisheng/wot-design-uni/commit/72a567c44867f049c1526f5d682e46155047516a))
* ✏️  增加steps设置激活项的介绍 ([7c0d36c](https://github.com/Moonofweisheng/wot-design-uni/commit/7c0d36c36214517d959aa27e284aacf59d2b074b))

### [1.2.3](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.2...v1.2.3) (2024-03-20)


### ✏️ Documentation | 文档

* ✏️  修复部分外部链接不可用的问题 ([e2fb515](https://github.com/Moonofweisheng/wot-design-uni/commit/e2fb5155c40e58e44920329cdfb4f2391bb81970))


### 🐛 Bug Fixes | Bug 修复

* 修复 checkbox 单独使用时无法切换选中状态的问题 ([#198](https://github.com/Moonofweisheng/wot-design-uni/issues/198)) ([4a20219](https://github.com/Moonofweisheng/wot-design-uni/commit/4a2021908f0d63ca398660950609bd5f4a7ca9b9))

### [1.2.2](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.1...v1.2.2) (2024-03-19)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复picker选择器绑定值value值为0时无法生效的问题 ([d4de4ba](https://github.com/Moonofweisheng/wot-design-uni/commit/d4de4ba93824905840adc49e2d85d7168c9fd48e)), closes [#195](https://github.com/Moonofweisheng/wot-design-uni/issues/195)

### [1.2.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.2.0...v1.2.1) (2024-03-19)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复部分可为null的props丢失null类型的问题 ([604e9f3](https://github.com/Moonofweisheng/wot-design-uni/commit/604e9f3810f38637ee1a7640e642790719cd9d3f))

## [1.2.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.10...v1.2.0) (2024-03-19)


### ✨ Features | 新功能

* ✨ typescript类型支持增强 ([#192](https://github.com/Moonofweisheng/wot-design-uni/issues/192)) ([201e7a1](https://github.com/Moonofweisheng/wot-design-uni/commit/201e7a12b9c4023e35ff9540f5da09794ea3c6f7))

### [1.1.10](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.9...v1.1.10) (2024-03-15)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复tabbar默认shape固定底部安全区无遮挡的问题 ([03048bb](https://github.com/Moonofweisheng/wot-design-uni/commit/03048bba17966e13e864c4a3d29047986778d586))

### [1.1.9](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.8...v1.1.9) (2024-03-11)


### ✏️ Documentation | 文档

* ✏️  推荐使用托管在gitee上的文档地址 ([24ae6ca](https://github.com/Moonofweisheng/wot-design-uni/commit/24ae6cac05b150ff8da3ab47cc4bb3eeac48a369))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复textarea在微信小程序平台下部分安卓手机maxlength不生效的问题 ([512e63b](https://github.com/Moonofweisheng/wot-design-uni/commit/512e63b0ad30ff7acb14fffbb124dba59e34d450))

### [1.1.8](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.7...v1.1.8) (2024-03-10)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复transitiont打开后立即关闭时无法关闭的问题 ([c1cebf6](https://github.com/Moonofweisheng/wot-design-uni/commit/c1cebf6de0b1c6a2f8a32aa4815adc8c0466eeca))

### [1.1.7](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.6...v1.1.7) (2024-03-09)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复badge组件多出一个script标签的问题 ([e1819b4](https://github.com/Moonofweisheng/wot-design-uni/commit/e1819b4dbcb3dc1244bbe5641cdb538d0ad9c5e7)), closes [#188](https://github.com/Moonofweisheng/wot-design-uni/issues/188)

### [1.1.6](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.5...v1.1.6) (2024-03-06)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Tabar为round类型时开启safeAreaInsetBottom是样式异常的问题 ([aa9ae88](https://github.com/Moonofweisheng/wot-design-uni/commit/aa9ae88f35236dec15635bd8e4fd0c7023e5fb70))

### [1.1.5](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.4...v1.1.5) (2024-03-05)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Search、Input等组件clear事件在value变更前触发的问题 ([8d113a2](https://github.com/Moonofweisheng/wot-design-uni/commit/8d113a285dbffa705e44a31ad05e48f36978c46c)), closes [#185](https://github.com/Moonofweisheng/wot-design-uni/issues/185)


### ✏️ Documentation | 文档

* ✏️  首页增加常见问题的入口 ([fd7a7dd](https://github.com/Moonofweisheng/wot-design-uni/commit/fd7a7dd287226f670cf02db9cf2856c9ebe52498))

### [1.1.4](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.3...v1.1.4) (2024-03-04)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Navbar显示下边框时fixed不生效的问题 ([18c09d8](https://github.com/Moonofweisheng/wot-design-uni/commit/18c09d87384699c044a9f84838b17f74eaff77b3))


### ✏️ Documentation | 文档

* ✏️  增加支付宝小程序styleIsolation配置的文档 ([68d1262](https://github.com/Moonofweisheng/wot-design-uni/commit/68d1262ed5ac6ccf8c078ba089e045739c2275e8))

### [1.1.3](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.2...v1.1.3) (2024-03-03)


### ✏️ Documentation | 文档

* ✏️  文档中增加提问的智慧和toast、message组件的常见问题 ([2fdd4b1](https://github.com/Moonofweisheng/wot-design-uni/commit/2fdd4b1c050ed8805637ec0b082ca4d20c3c17b6))

### [1.1.2](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.1...v1.1.2) (2024-03-01)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Button会存在一个默认border的问题 ([#182](https://github.com/Moonofweisheng/wot-design-uni/issues/182)) ([3714ffb](https://github.com/Moonofweisheng/wot-design-uni/commit/3714ffb0f1de55f3eae5a36b9235bdbc518b0738))

### [1.1.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.1.0...v1.1.1) (2024-02-29)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 ImgCropper 在钉钉小程序平台无法展示所选图片的问题 ([db8d029](https://github.com/Moonofweisheng/wot-design-uni/commit/db8d029db3a227cd062c6e9d7c14eb9d0f3dae87))
* 🐛 修复微信小程序css使用标签选择器抛出警告的问题（wd-img） ([#181](https://github.com/Moonofweisheng/wot-design-uni/issues/181)) ([ee3da01](https://github.com/Moonofweisheng/wot-design-uni/commit/ee3da016439d68f1d9d061bb1b1cbd10f2863aa4))

## [1.1.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v1.0.0...v1.1.0) (2024-02-27)


### ✨ Features | 新功能

* ✨ Button 支持微信小程序chooseavatar和agreeprivacyauthorization事件 ([177c726](https://github.com/Moonofweisheng/wot-design-uni/commit/177c726e0d0f9bc435f815af2e4fc8e917ef144d))
* ✨ DropDown组件支持钉钉小程序 ([924c65b](https://github.com/Moonofweisheng/wot-design-uni/commit/924c65b4f66e7d1ff75aa2bcb5c6cbe4e045fb92))
* ✨ Slider 支持钉钉小程序 ([bea7a49](https://github.com/Moonofweisheng/wot-design-uni/commit/bea7a499edc3e0681ec11dd26333d8a969dd9b0e))


### ✏️ Documentation | 文档

* ✏️  新增支持钉钉小程序平台 ([9525f23](https://github.com/Moonofweisheng/wot-design-uni/commit/9525f23f207af47c10c37614279b8318500e3ede))
* ✏️  修复Segment演示demo在钉钉小程序平台展示异常的问题 ([4fbe556](https://github.com/Moonofweisheng/wot-design-uni/commit/4fbe5569b3cb1761c2ef7847a4abc1721d3ed220))
* ✏️  支持平台的介绍中新增支付宝小程序 ([320e4dd](https://github.com/Moonofweisheng/wot-design-uni/commit/320e4dd2e102146b8a466d378c8356286faeb28c))

## [1.0.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.3.1...v1.0.0) (2024-02-26)


### ✨ Features | 新功能

* ✨ 支持支付宝小程序并提供ConfigProvider的便利性优化 ([e1baca3](https://github.com/Moonofweisheng/wot-design-uni/commit/e1baca3ce5201c38e6b0aee5e6cb7099ecba9772))
* ✨ sticky组件支持支付宝平台 ([3805882](https://github.com/Moonofweisheng/wot-design-uni/commit/3805882b8b1f8ad165d8cda528fa0b5cf3f96c27))


### ✏️ Documentation | 文档

* ✏️  演示demo增加组件库版本号的显示 ([19a00e3](https://github.com/Moonofweisheng/wot-design-uni/commit/19a00e34652bf672f7fdfd28899625dac977a1b6))

### [0.3.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.3.0...v0.3.1) (2024-02-26)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复大尺寸checkbox选中样式异常的问题 ([413cf3b](https://github.com/Moonofweisheng/wot-design-uni/commit/413cf3b6a04358bca5284704d750cfd224c1d277))

## [0.3.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.23...v0.3.0) (2024-02-21)


### ✨ Features | 新功能

* ✨ 调整Button 按钮样式受主色控制并优化幽灵按钮显示效果 ([5f0d1f5](https://github.com/Moonofweisheng/wot-design-uni/commit/5f0d1f5f8bac81435d248348ec5823e82d35f7a4))


### ✏️ Documentation | 文档

* ✏️  修复select-picker组件绑定值描述错误的问题 ([b287111](https://github.com/Moonofweisheng/wot-design-uni/commit/b2871114311656580ade25c25f0775d9246f32b7)), closes [#178](https://github.com/Moonofweisheng/wot-design-uni/issues/178)
* ✏️  增加Button细边框幽灵按钮的使用说明 ([366c34b](https://github.com/Moonofweisheng/wot-design-uni/commit/366c34b69faedee4874221c604d1a6d5c8c1a8e0))

### [0.2.23](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.22...v0.2.23) (2024-02-07)


### ✏️ Documentation | 文档

* ✏️  文档支持显示友情链接 ([adac43f](https://github.com/Moonofweisheng/wot-design-uni/commit/adac43f2a1b300d4169249e108389b3a24ac9ffa))
* ✏️  友情链接支持来自线上配置 ([50896bb](https://github.com/Moonofweisheng/wot-design-uni/commit/50896bb475c5e1c5be42bdc22a56a47db9a51481))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 MessageBox 国际化不生效的问题 ([48404a1](https://github.com/Moonofweisheng/wot-design-uni/commit/48404a1ccccf9081cf3e9f9918e20cb58a6bb12f)), closes [#177](https://github.com/Moonofweisheng/wot-design-uni/issues/177)

### [0.2.22](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.21...v0.2.22) (2024-01-30)


### ✏️ Documentation | 文档

* ✏️  修正 Popover 组件文档中menuclick示例错误的问题 ([9df96e7](https://github.com/Moonofweisheng/wot-design-uni/commit/9df96e7629296bfba2b7f951b83508cc9e4a3935))

### [0.2.21](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.19...v0.2.21) (2024-01-28)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Dropdown 组件点击已展开项时无法关闭的问题 ([3846590](https://github.com/Moonofweisheng/wot-design-uni/commit/38465905e76b929a4d4af93dfe4ad9f31503b2ad))


### ✨ Features | 新功能

* ✨ 支持国际化 ([#168](https://github.com/Moonofweisheng/wot-design-uni/issues/168)) ([ce9f192](https://github.com/Moonofweisheng/wot-design-uni/commit/ce9f19244e4d376c5104be2b89d57426bf722aee))

### [0.2.19](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.18...v0.2.19) (2024-01-25)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Input 绑定值无法为null的问题 ([f745e14](https://github.com/Moonofweisheng/wot-design-uni/commit/f745e14d10d64b643aa2f9c0170f72db3a99bb3e))
* 🐛 修复多列、单复选选择器列更新时显示值未更新的问题 ([5ba45b9](https://github.com/Moonofweisheng/wot-design-uni/commit/5ba45b94b711e1129873fa29b243a616087ddc2a)), closes [#167](https://github.com/Moonofweisheng/wot-design-uni/issues/167)

### [0.2.18](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.17...v0.2.18) (2024-01-24)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 CheckBox 组件的选中色无法应用到css变量的问题 ([febfb61](https://github.com/Moonofweisheng/wot-design-uni/commit/febfb61cab3b55a9f78920ed4715f05a232fb9f6))
* 🐛 修复 Radio 组件的选中色无法应用到css变量的问题 ([b7e8631](https://github.com/Moonofweisheng/wot-design-uni/commit/b7e86314139cc6291e851c350219fce837f0f26e))

### [0.2.17](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.16...v0.2.17) (2024-01-23)


### ✨ Features | 新功能

* ✨ Form 表单组件提供开关控制是否model变化时重置提示信息 ([b9f46ba](https://github.com/Moonofweisheng/wot-design-uni/commit/b9f46ba0da85acd7312753a34dd0ad3f2f7379a2)), closes [#166](https://github.com/Moonofweisheng/wot-design-uni/issues/166)

### [0.2.16](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.15...v0.2.16) (2024-01-21)


### ✏️ Documentation | 文档

* ✏️  修复文档中关于日期选择器最大日期描述错误的问题 ([bb76ce3](https://github.com/Moonofweisheng/wot-design-uni/commit/bb76ce332c6977cbae981790aa356bc27c0d9efa))

### [0.2.15](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.14...v0.2.15) (2024-01-15)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Tab class类名属性错误的问题 ([dc87df7](https://github.com/Moonofweisheng/wot-design-uni/commit/dc87df70c7920d7cfabc571ace8beb2ce0dc2a7c))


### ✏️ Documentation | 文档

* ✏️  修复 Upload 文档中before-upload钩子参数描述错误的问题 ([3ec7299](https://github.com/Moonofweisheng/wot-design-uni/commit/3ec7299ce724fce771b782f6110ba6ec29376291))

### [0.2.14](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.13...v0.2.14) (2024-01-14)


### ✨ Features | 新功能

* ✨ InputNumber步进器支持禁用输入框 ([933bce3](https://github.com/Moonofweisheng/wot-design-uni/commit/933bce314618956ff922daacef5b3594f36baf9b))

### [0.2.13](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.12...v0.2.13) (2024-01-12)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复演示文档刷新后指向localhost的问题 ([4cd8b16](https://github.com/Moonofweisheng/wot-design-uni/commit/4cd8b16bdf5ea8e93cff85396025844e9ebd031c))

### [0.2.12](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.11...v0.2.12) (2024-01-11)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Tabbar 错误地抛出提示信息的问题 ([1c89ed0](https://github.com/Moonofweisheng/wot-design-uni/commit/1c89ed0a7158e79556e83c6aeb89bd1e93e4ea0d))


### ✏️ Documentation | 文档

* ✏️  增加 Circle 组件使用插槽的示例 ([6341594](https://github.com/Moonofweisheng/wot-design-uni/commit/6341594295732dac951c465d95537f36addbbf9c))
* ✏️  增加展示gitee的star徽标 ([1abf962](https://github.com/Moonofweisheng/wot-design-uni/commit/1abf962e37e27c1c1688bed2026c66022e63ce9b))

### [0.2.11](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.10...v0.2.11) (2024-01-09)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Overlay 类型声明错误的问题 ([930e59a](https://github.com/Moonofweisheng/wot-design-uni/commit/930e59a9b09aee535ec4c316e44ed3c0e31be628))


### ✏️ Documentation | 文档

* ✏️  提供托管在Giteee上的文档网站 ([6d62e9e](https://github.com/Moonofweisheng/wot-design-uni/commit/6d62e9e7ddda0bd9f51f2ad9e2893f1ed3709c63))

### [0.2.10](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.9...v0.2.10) (2024-01-08)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 col-picker 暗黑模式下标题文字颜色不清楚的问题 ([217ffb7](https://github.com/Moonofweisheng/wot-design-uni/commit/217ffb7dacb66b2017145c6e43fc8c873a6e9dd2))
* 🐛 修复 steps 组件自定义图标显示异常的问题 ([0300f63](https://github.com/Moonofweisheng/wot-design-uni/commit/0300f63f35a5afcd278aba3b4ab721f498716d94))
* 🐛 修复支付宝小程序暗黑模式下 Input、Textarea 组件显示异常的问题 ([8a9c344](https://github.com/Moonofweisheng/wot-design-uni/commit/8a9c344872bfcd81a73f71520f51b6b849f516d5))

### [0.2.9](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.8...v0.2.9) (2024-01-07)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 col-picker 组件首次打开指示线位置异常的问题 ([323fb00](https://github.com/Moonofweisheng/wot-design-uni/commit/323fb00942b7032b678d92ab03360dc7bb8faae8))

### [0.2.8](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.7...v0.2.8) (2024-01-06)


### ✏️ Documentation | 文档

* ✏️  展示netlify支持 ([410b180](https://github.com/Moonofweisheng/wot-design-uni/commit/410b180ec9c660ab9c49d6eb203d53c35919c512))

### [0.2.7](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.5...v0.2.7) (2024-01-06)


### ✨ Features | 新功能

* ✨ 优化provide/inject的使用方式 ([892f467](https://github.com/Moonofweisheng/wot-design-uni/commit/892f4675a848ee3d4c965c36d5c4034aa5806b6d))


### ✏️ Documentation | 文档

* ✏️  文档网站增加自定义footer ([dd8bc00](https://github.com/Moonofweisheng/wot-design-uni/commit/dd8bc003eedcdc43cdd60bb896c897d108dd4a51))
* ✏️  修复vitepress自定义footer展示错误的问题 ([c0701d5](https://github.com/Moonofweisheng/wot-design-uni/commit/c0701d584e5d9b84e6d913dd23666b80d803407b))

### [0.2.6](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.5...v0.2.6) (2024-01-06)


### ✨ Features | 新功能

* ✨ 优化provide/inject的使用方式 ([892f467](https://github.com/Moonofweisheng/wot-design-uni/commit/892f4675a848ee3d4c965c36d5c4034aa5806b6d))


### ✏️ Documentation | 文档

* ✏️  文档网站增加自定义footer ([dd8bc00](https://github.com/Moonofweisheng/wot-design-uni/commit/dd8bc003eedcdc43cdd60bb896c897d108dd4a51))

### [0.2.5](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.4...v0.2.5) (2023-12-28)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Form 导入FormRules、ErrorMessage时未指定为type的问题 ([c88c84e](https://github.com/Moonofweisheng/wot-design-uni/commit/c88c84e8b71fc2404643a623c28f4953ffe36e71))
* 🐛 修复 SwipeAction 组件在H5端导致页面无法上下滚动的问题 ([1f68ce1](https://github.com/Moonofweisheng/wot-design-uni/commit/1f68ce13c8109dd92ca4bf055f66aa8dff24c83d)), closes [#149](https://github.com/Moonofweisheng/wot-design-uni/issues/149)

### [0.2.4](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.3...v0.2.4) (2023-12-27)

### [0.2.3](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.2...v0.2.3) (2023-12-27)


### ✏️ Documentation | 文档

* ✏️  修复动态表单演示页面标题错误的问题 ([7c65359](https://github.com/Moonofweisheng/wot-design-uni/commit/7c65359c88614ae53c4800d0946d8e09ef547a26))

### [0.2.2](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.1...v0.2.2) (2023-12-26)


### 🐛 Bug Fixes | Bug 修复

* 🐛 移除 Swiper 的list中不必填的属性 ([672f680](https://github.com/Moonofweisheng/wot-design-uni/commit/672f680051009edd23a67d4e32722839f519d9ba))


### ✏️ Documentation | 文档

* ✏️  修复 Textarea 文档示例错误的问题 ([7da3a4c](https://github.com/Moonofweisheng/wot-design-uni/commit/7da3a4cdfe35f87193f57139de0819130424d6ea))
* ✏️  优化 Form 相关组件的文档介绍 ([6a0a7a7](https://github.com/Moonofweisheng/wot-design-uni/commit/6a0a7a730fcd8904609f8a48339e1079baf3e9be))

### [0.2.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.2.0...v0.2.1) (2023-12-25)


### ✏️ Documentation | 文档

* ✏️  PassowrdInput 密码输入框组件增加文档标题 ([cccc4cc](https://github.com/Moonofweisheng/wot-design-uni/commit/cccc4cc0d4952f594e5387bad32a7248009ebd9e))

## [0.2.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.70...v0.2.0) (2023-12-24)


### ✨ Features | 新功能

* ✨ 新增 Form 表单组件 ([c8086d6](https://github.com/Moonofweisheng/wot-design-uni/commit/c8086d624c01bfbae3b9dfc324d33d6bfe46e041))
* ✨ 新增 PasswordInput 密码输入框 ([b8c68f9](https://github.com/Moonofweisheng/wot-design-uni/commit/b8c68f92f7ddbf3ae2a58bf36593a0cd0340f225))
* ✨ 新增 textarea 文本域组件 ([ed56bcd](https://github.com/Moonofweisheng/wot-design-uni/commit/ed56bcdb0337c198e8834182d47d1cd83513091d))
* ✨ 增加父子组件 Provide/Inject 的hooks ([eb971d4](https://github.com/Moonofweisheng/wot-design-uni/commit/eb971d4e86733b0337de0c63f26b373424a842f0))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Input 组件在支付宝平台存在一个默认padding的问题 ([921c906](https://github.com/Moonofweisheng/wot-design-uni/commit/921c90606e05ea1b29cdee39cde7858907b2cd4d))


### ✏️ Documentation | 文档

* ✏️  调整主题定制相关文档 ([2d5b1af](https://github.com/Moonofweisheng/wot-design-uni/commit/2d5b1af2787d33ec24eb6e6ab9db6ff941d80c2b))
* ✏️  增加 textarea 文本域组件功能的迁移说明 ([61ebc7d](https://github.com/Moonofweisheng/wot-design-uni/commit/61ebc7db1e1db3e8634a87c24fae0869f4b6f17b))
* ✏️  PasswordInput 密码输入框文档增加版本介绍 ([56ccf60](https://github.com/Moonofweisheng/wot-design-uni/commit/56ccf6005e502237e1ff45b5b52e852b402a10c9))

### [0.1.70](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.69...v0.1.70) (2023-12-23)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 picker 的列value为0时回显异常的问题 ([2ba93c3](https://github.com/Moonofweisheng/wot-design-uni/commit/2ba93c328c54c41b6e1e9ead946e9c46e278ebb9)), closes [#155](https://github.com/Moonofweisheng/wot-design-uni/issues/155)

### [0.1.69](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.68...v0.1.69) (2023-12-13)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Notify 使用文档错误的问题 ([3fe6423](https://github.com/Moonofweisheng/wot-design-uni/commit/3fe6423c9c6727eaa3655029bf862b9be182b70b)), closes [#148](https://github.com/Moonofweisheng/wot-design-uni/issues/148)

### [0.1.68](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.67...v0.1.68) (2023-12-07)


### ✏️ Documentation | 文档

* ✏️  增加 Gap 组件的文档 ([9e3a8c1](https://github.com/Moonofweisheng/wot-design-uni/commit/9e3a8c1506f9ef93f9f0155ac948c8bc4d215ea8))

### [0.1.67](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.66...v0.1.67) (2023-12-06)


### ✨ Features | 新功能

* ✨ 新增 Gap 间隔槽组件 ([#147](https://github.com/Moonofweisheng/wot-design-uni/issues/147)) ([fa7cd16](https://github.com/Moonofweisheng/wot-design-uni/commit/fa7cd163810cbcd63fc2a99a6a92ccd8a803b2f1))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Picker组件v-model数据不更新的问题 ([e60f475](https://github.com/Moonofweisheng/wot-design-uni/commit/e60f47538130625a8c8f93e3a6795b7e71588002)), closes [#146](https://github.com/Moonofweisheng/wot-design-uni/issues/146)

### [0.1.66](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.65...v0.1.66) (2023-12-01)


### ✨ Features | 新功能

* ✨ Tag 组件type增加属性值default ([#143](https://github.com/Moonofweisheng/wot-design-uni/issues/143)) ([48f0e4b](https://github.com/Moonofweisheng/wot-design-uni/commit/48f0e4b3c484755a0da4eb11fa814c6b52be74b1))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 ActionSheet 组件设置custom-class无效的问题 ([1e5bcf7](https://github.com/Moonofweisheng/wot-design-uni/commit/1e5bcf7d0a8be66b80de81a0359d3fb429fdf52d))

### [0.1.65](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.64...v0.1.65) (2023-11-29)


### ✨ Features | 新功能

* ✨ 新增 NumberKeyboard 数字键盘组件 ([#139](https://github.com/Moonofweisheng/wot-design-uni/issues/139)) ([7bb4455](https://github.com/Moonofweisheng/wot-design-uni/commit/7bb4455c6d9eca179ba174b8ad0da6ae8a09defc))
* Tag新增类型标签添加slot ([7977dbb](https://github.com/Moonofweisheng/wot-design-uni/commit/7977dbbe3565eb06b700f2c55a31803647760fa9))


### ✏️ Documentation | 文档

* ✏️  新增关于 Tag 组件的add插槽的介绍 ([537fa5e](https://github.com/Moonofweisheng/wot-design-uni/commit/537fa5ebf8dcdc14ee12fc1f1e5a5f829653a14a))

### [0.1.64](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.63...v0.1.64) (2023-11-25)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复DateTimePicker标题展示和模式为区间时before-confirm参数错误的问题 ([7bcd12b](https://github.com/Moonofweisheng/wot-design-uni/commit/7bcd12ba633224dd42b160f0b43fadc99ac76707))

### [0.1.63](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.62...v0.1.63) (2023-11-23)


### ✨ Features | 新功能

* ✨ CountDown 倒计时组件支持custom-class和custom-style ([dd60d6d](https://github.com/Moonofweisheng/wot-design-uni/commit/dd60d6df855cc49eba36beb7bcfb9eeb96ed0c82))
* ✨ Search 搜索组件支持自动聚焦和清空后自动聚焦 ([2e3ab63](https://github.com/Moonofweisheng/wot-design-uni/commit/2e3ab63d3dad582c44d9d9f8d2b57e21ee22f380))


### ✏️ Documentation | 文档

* ✏️  修复文档中config-provider等页面的错误 ([f71e641](https://github.com/Moonofweisheng/wot-design-uni/commit/f71e6412e770f2043e4cb9a35950a68aacf0e542))

### [0.1.62](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.61...v0.1.62) (2023-11-22)


### ✨ Features | 新功能

* ✨ Badge 徽标组件支持控制是否显示0值 ([65cfb2c](https://github.com/Moonofweisheng/wot-design-uni/commit/65cfb2ca15c29783f6de48de0bda4ddc3d43664a))
* ✨ Upload 上传组件支持h5端获取文件后缀名 ([59fe70c](https://github.com/Moonofweisheng/wot-design-uni/commit/59fe70c0d0c55c7ecca26902b41599be94e34fe3))


### ✏️ Documentation | 文档

* ✏️  优化 Upload 文档中关于云存储内容的样式 ([607ce05](https://github.com/Moonofweisheng/wot-design-uni/commit/607ce05100ffef9c5c98b503e580a718551f5552))

### [0.1.61](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.60...v0.1.61) (2023-11-20)


### ✏️ Documentation | 文档

* ✏️  修复 Tag 标签组件文档错乱的问题 ([502203b](https://github.com/Moonofweisheng/wot-design-uni/commit/502203bebf08d7e6851b8dd2f94768f6c620da26))
* ✏️  ImgCropper 组件演示页面增加微信小程序隐私协议弹框 ([3af9246](https://github.com/Moonofweisheng/wot-design-uni/commit/3af9246d1f1876ba3c83f6829a9716ad37ae5829))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Calendar 日历组件存在未定义属性导致警告的问题 ([d0d3152](https://github.com/Moonofweisheng/wot-design-uni/commit/d0d3152a5ca1a3e06135b12f4565211258619d1f))
* 🐛 修复 CountDown 倒计时组件文档手动控制示例错误的问题 ([3c373fb](https://github.com/Moonofweisheng/wot-design-uni/commit/3c373fbf3af02c8a73ddd3a4063f9408ec4f33ff))
* 🐛 修复 SelectPicker 组件loading状态仍可以滚动的问题 ([c5a1bff](https://github.com/Moonofweisheng/wot-design-uni/commit/c5a1bfff576aad8a6f03288a6a12a8ed24f776f2))


### ✨ Features | 新功能

* ✨ Upload 上传组件支持上传至云存储 ([523a6be](https://github.com/Moonofweisheng/wot-design-uni/commit/523a6be40b065c4cc28cfe81fdcb200142d9a455))

### [0.1.60](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.59...v0.1.60) (2023-11-20)


### ✏️ Documentation | 文档

* ✏️  修复 CountDown 倒计时组件默认slot用法的介绍错误的问题 ([2fbb88f](https://github.com/Moonofweisheng/wot-design-uni/commit/2fbb88fcd9fe21de09637d48149278d9aef5e1d9))

### [0.1.59](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.58...v0.1.59) (2023-11-19)

### [0.1.58](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.57...v0.1.58) (2023-11-19)


### ✨ Features | 新功能

* ✨ 新增 CountDown 倒计时组件 ([a805d04](https://github.com/Moonofweisheng/wot-design-uni/commit/a805d04827600525c08fbc1848cb3a524b48e81a))
* ✨ CountDown 倒计时组件支持小程序 ([22f249a](https://github.com/Moonofweisheng/wot-design-uni/commit/22f249ae5bdedea1ecebfe31350c7b5a1e394f2e))

### [0.1.57](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.56...v0.1.57) (2023-11-17)


### ✨ Features | 新功能

* ✨ 新增 Fab 悬浮动作按钮组件 ([3c526fe](https://github.com/Moonofweisheng/wot-design-uni/commit/3c526feb102ccf17da0678a6e1c004bb82d57f0f))


### ✏️ Documentation | 文档

* ✏️  修复 Cell 组件点击反馈的介绍错误的问题 ([9096d81](https://github.com/Moonofweisheng/wot-design-uni/commit/9096d81f514348d6103f33590387d0406b217198))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Table 表格组件数字英文换行失效的问题 ([47ac339](https://github.com/Moonofweisheng/wot-design-uni/commit/47ac3390cb58d250129a9db6ade8d90f980855a1))

### [0.1.56](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.55...v0.1.56) (2023-11-12)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Tabbar 标签栏组件 bordered 属性无效的问题 ([69c2656](https://github.com/Moonofweisheng/wot-design-uni/commit/69c265638c6fc36a1479a37c052a572da333006f))

### [0.1.55](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.54...v0.1.55) (2023-11-12)


### 🐛 Bug Fixes | Bug 修复

* 修正README.md 中[贡献指南]链接错误，跳转失败的问题。 ([071de88](https://github.com/Moonofweisheng/wot-design-uni/commit/071de88f98fa90492032193606941fbcdfe9283d))


### ✨ Features | 新功能

* ✨ Swiper 轮播图组件增加imageMode属性支持自定义图片裁剪缩放模式 ([4205c01](https://github.com/Moonofweisheng/wot-design-uni/commit/4205c0149d3224e318ef21563e96f0a843ad13bc))

### [0.1.54](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.53...v0.1.54) (2023-11-10)


### 🐛 Bug Fixes | Bug 修复

* 修正ColPicker多列选择器v-model类型提示使用Record<string, any>[]实际上的数据是单维数组的问题 ([c490ac6](https://github.com/Moonofweisheng/wot-design-uni/commit/c490ac607e6f9eb7207b90943b83ce1696a30fd4))

### [0.1.53](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.52...v0.1.53) (2023-11-09)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Input 输入框组件包含无用显示内容的问题 ([13a3106](https://github.com/Moonofweisheng/wot-design-uni/commit/13a3106055d66083d640912bfff67f2e08781f03))

### [0.1.52](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.51...v0.1.52) (2023-11-09)


### ✨ Features | 新功能

* ✨ Input绑定值为null时的警告从error改为warn ([70f1407](https://github.com/Moonofweisheng/wot-design-uni/commit/70f1407eec057668da0117c9861b523ab667da70))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Badge 徽标组件暗黑模式下边框颜色未兼容的问题 ([c0a0b5a](https://github.com/Moonofweisheng/wot-design-uni/commit/c0a0b5aaea238f144978a3a9b2ab6db202dfdc21)), closes [#115](https://github.com/Moonofweisheng/wot-design-uni/issues/115)

### [0.1.51](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.50...v0.1.51) (2023-11-06)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复引入错误依赖的问题 ([15bc876](https://github.com/Moonofweisheng/wot-design-uni/commit/15bc876a3026319c88ce4b757593243f9869ce39))

### [0.1.50](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.49...v0.1.50) (2023-11-06)


### ✨ Features | 新功能

* ✨ SidebarItem、TabbarItem、GridItem组件增加徽标自定义属性badge-props ([7143098](https://github.com/Moonofweisheng/wot-design-uni/commit/71430989bff81a137fc5199eeeefe0a4da3ce31d))

### [0.1.49](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.48...v0.1.49) (2023-11-05)


### ✨ Features | 新功能

* ✨ 新增 Sidebar 侧边栏组件 ([#113](https://github.com/Moonofweisheng/wot-design-uni/issues/113)) ([d189378](https://github.com/Moonofweisheng/wot-design-uni/commit/d189378a02aeb36c275218358849a187e88ce687))

### [0.1.48](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.47...v0.1.48) (2023-11-04)


### ✨ Features | 新功能

* ✨ Input 输入框增加支持微信小程序原生属性always-embed ([57d00ec](https://github.com/Moonofweisheng/wot-design-uni/commit/57d00ececd9849a2d21f59fc863b6ad8933419d8))

### [0.1.47](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.46...v0.1.47) (2023-11-02)


### ✏️ Documentation | 文档

* ✏️  修复 Image 图片组件演示demo样式不友好的问题 ([cbdab19](https://github.com/Moonofweisheng/wot-design-uni/commit/cbdab1959efff642a60a351ffd6454dcd173b9a4))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Input 组件为textarea类型时show-confirm-bar不生效的问题 ([eebec8a](https://github.com/Moonofweisheng/wot-design-uni/commit/eebec8a74fa9399bfd305cadb1a9b6a526e88568))
* 🐛 img 预览demo布局错位 ([0397809](https://github.com/Moonofweisheng/wot-design-uni/commit/0397809d81010e109c238eefea56c82bd06a50cb))


### ✨ Features | 新功能

* ✨ 图片添加圆角大小设置 ([98edaed](https://github.com/Moonofweisheng/wot-design-uni/commit/98edaed6cb1c81b9ed6e05561814aaa51b31b2b3))
* ✨ Badge 徽标组件增加customStyle自定义样式 ([d194106](https://github.com/Moonofweisheng/wot-design-uni/commit/d194106f07fb46f6a979967ba327dee1b41ca70e))

### [0.1.46](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.45...v0.1.46) (2023-11-02)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Tabs 组件在微信小程序端有概率不绘制下划线的问题 ([d70ec65](https://github.com/Moonofweisheng/wot-design-uni/commit/d70ec65998d1ca1fbdbd3b4ef1af4a292a597f55))

### [0.1.45](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.44...v0.1.45) (2023-11-01)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Switch 开关组件自定义大小无效的问题 ([15ffa1f](https://github.com/Moonofweisheng/wot-design-uni/commit/15ffa1f9172d3c11722c71ca370c1016e25f2b41))

### [0.1.44](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.43...v0.1.44) (2023-11-01)


### ✨ Features | 新功能

* ✨ 使用 useQueue hook替换clickOut实现关闭多个气泡等组件的功能 ([3a24999](https://github.com/Moonofweisheng/wot-design-uni/commit/3a24999c1e28414b12806cad3c3bc6f7a445593f))


### ✏️ Documentation | 文档

* ✏️  优化演示项目首页样式 ([e98f30e](https://github.com/Moonofweisheng/wot-design-uni/commit/e98f30e0125e287d2bc5cdbcfff69a35cb7436d8))

### [0.1.43](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.42...v0.1.43) (2023-10-31)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Tooltip 文字提示组件微信端关闭按钮样式错误的问题 ([ec1b949](https://github.com/Moonofweisheng/wot-design-uni/commit/ec1b9490b9517d0e4ebe4a5130a418a684b1a589))

### [0.1.42](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.41...v0.1.42) (2023-10-31)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Popover、Tooltip 组件展开过程中无法遮盖图片的问题 ([db009c1](https://github.com/Moonofweisheng/wot-design-uni/commit/db009c1229413f7ecc446e3e3a1db4d7678268b3))

### [0.1.41](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.40...v0.1.41) (2023-10-31)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Popover、Tooltip 组件展开切换动画不流畅的问题 ([c765b08](https://github.com/Moonofweisheng/wot-design-uni/commit/c765b085a6018433bb95fd7647c091393e2e6e7c)), closes [#101](https://github.com/Moonofweisheng/wot-design-uni/issues/101)

### [0.1.40](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.39...v0.1.40) (2023-10-30)


### ✏️ Documentation | 文档

* ✏️  移除文档中 Search 组件未支持的插槽 ([325e140](https://github.com/Moonofweisheng/wot-design-uni/commit/325e14046de38eb7c4e792a64a290c9997af5cba))

### [0.1.39](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.38...v0.1.39) (2023-10-29)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 DateTimePicker 日期选择器组件双向绑定错误的问题 ([1b13873](https://github.com/Moonofweisheng/wot-design-uni/commit/1b13873cef4b7c760698abce7dca500cd548c660))


### ✨ Features | 新功能

* ✨ 新增 Table 表格组件 ([#98](https://github.com/Moonofweisheng/wot-design-uni/issues/98)) ([c8395f8](https://github.com/Moonofweisheng/wot-design-uni/commit/c8395f8a7e1ad041b003672081b715a7c755adc1))
* ✨ Collapse 折叠面板组件增加分割线 ([adc6633](https://github.com/Moonofweisheng/wot-design-uni/commit/adc6633ad80b74e801a48a50917c9a2d378de9e0)), closes [#97](https://github.com/Moonofweisheng/wot-design-uni/issues/97)

### [0.1.38](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.37...v0.1.38) (2023-10-25)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Upload 组件showLimitNum属性名拼写错误的问题 ([301cbff](https://github.com/Moonofweisheng/wot-design-uni/commit/301cbff20facf60b5645108972f22c8c14235f87))

### [0.1.37](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.36...v0.1.37) (2023-10-25)


### ✏️ Documentation | 文档

* ✏️  修复Tabbar组件类型警告的问题并优化文档相关链接 ([28c3cef](https://github.com/Moonofweisheng/wot-design-uni/commit/28c3cefdfc24f3f6f2669de5f93456a0db281f72))
* ✏️  issues 模板更新 ([e34c5cd](https://github.com/Moonofweisheng/wot-design-uni/commit/e34c5cded1c850d22165c544a3c6a351701479ec))

### [0.1.36](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.35...v0.1.36) (2023-10-21)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Tabs 指定name时双向绑定值仍为index的问题 ([756cad8](https://github.com/Moonofweisheng/wot-design-uni/commit/756cad8d6fcaefb0bf73f1cd2b9f4a24f5827b37)), closes [#91](https://github.com/Moonofweisheng/wot-design-uni/issues/91)

### [0.1.35](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.34...v0.1.35) (2023-10-21)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Tabs 组件通过name匹配时抛出异常的问题 ([b36fb1f](https://github.com/Moonofweisheng/wot-design-uni/commit/b36fb1f9def0aa99c521ffc20c6cae8074b56838)), closes [#91](https://github.com/Moonofweisheng/wot-design-uni/issues/91)

### [0.1.34](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.33...v0.1.34) (2023-10-18)


### ✨ Features | 新功能

* ✨ SelectPicker 单复选选择器增加 scroll-into-view 属性支持定位到选中值 ([00b27c1](https://github.com/Moonofweisheng/wot-design-uni/commit/00b27c187438426471dab2ea15bc39d627773ab2)), closes [#68](https://github.com/Moonofweisheng/wot-design-uni/issues/68)

### [0.1.33](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.32...v0.1.33) (2023-10-17)


### ✨ Features | 新功能

* ✨ 新增 Navbar 导航栏组件 ([#86](https://github.com/Moonofweisheng/wot-design-uni/issues/86)) ([5f66f71](https://github.com/Moonofweisheng/wot-design-uni/commit/5f66f713fae9e693fda4a472bc3e0767a1a6d3e8))

### [0.1.32](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.31...v0.1.32) (2023-10-16)


### ✨ Features | 新功能

* ✨ Tabs 组件调整为受控组件 ([b2a70bc](https://github.com/Moonofweisheng/wot-design-uni/commit/b2a70bc8f32800119924b29283db760af8a3ddb1)), closes [#82](https://github.com/Moonofweisheng/wot-design-uni/issues/82)

### [0.1.31](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.30...v0.1.31) (2023-10-16)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复wd-popup组件的click-modal方法错误调用的问题 ([224e3e5](https://github.com/Moonofweisheng/wot-design-uni/commit/224e3e53f912a236e8e24f8afef64fa09f6a89aa))


### ✏️ Documentation | 文档

* ✏️  更新文档中关于组件数量的介绍 ([4fa7355](https://github.com/Moonofweisheng/wot-design-uni/commit/4fa7355e69a8bb32d1c9924cfc717caaf3345ee5))
* ✏️  增加 Overlay 遮罩层组件的文档 ([3bca4b6](https://github.com/Moonofweisheng/wot-design-uni/commit/3bca4b65c2bc7cd7f90e8b9513c619799f9a438b))

### [0.1.30](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.29...v0.1.30) (2023-10-14)


### ✨ Features | 新功能

* ✨ 新增 Overlay 遮罩层组件 ([41b2c4f](https://github.com/Moonofweisheng/wot-design-uni/commit/41b2c4f3677dcce9c3e18875b7a1ec32d02b3d6a))
* ✨ Popup 弹出层组件增加 lock-scroll 属性 ([a6987e6](https://github.com/Moonofweisheng/wot-design-uni/commit/a6987e65da2f121d4e95c1b5cb271ed8f9a06e58))

### [0.1.29](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.28...v0.1.29) (2023-10-13)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Tabbar 标签栏组件固定底部时不设置placeholder情况下仍占据相应空间的问题 ([b58f9df](https://github.com/Moonofweisheng/wot-design-uni/commit/b58f9dfc8230e9427f2af658c8315c5b022b232c))

### [0.1.28](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.27...v0.1.28) (2023-10-12)


### ✏️ Documentation | 文档

* ✏️  文档样式优化 ([f21ecb9](https://github.com/Moonofweisheng/wot-design-uni/commit/f21ecb9dfbfbf0afe49a760d24ebdc08e4799717))

### [0.1.27](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.26...v0.1.27) (2023-10-12)


### ⚡ Performance Improvements | 性能优化

* ⚡ Segmented 分段器组件兼容支付宝小程序 ([3fd57a6](https://github.com/Moonofweisheng/wot-design-uni/commit/3fd57a600b537b945a8224bb97344baa9b0929e0))


### ✏️ Documentation | 文档

* ✏️  调整文档中图片的CDN地址 ([f704568](https://github.com/Moonofweisheng/wot-design-uni/commit/f704568f0299acbd3cc2d88934bfa1641b6cc792))
* ✏️  修复文档中根节点错写为根结点的问题 ([6da4145](https://github.com/Moonofweisheng/wot-design-uni/commit/6da414503ceb8b6de680b10332af45dbbbe7229d))


### ✨ Features | 新功能

* ✨ 新增 Tabbar 标签栏组件 ([aa0bf19](https://github.com/Moonofweisheng/wot-design-uni/commit/aa0bf194865e381af41f7afa18e46105e0c812a9))
* ✨ Icon 组件新增图标并支持第三方iconfont ([bc241ac](https://github.com/Moonofweisheng/wot-design-uni/commit/bc241acfc103309f196f4c04e08288526a056073))

### [0.1.26](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.25...v0.1.26) (2023-10-10)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Slider 滑块组件双向滑动时右边滑块百分比计算错误的问题 ([92db0f2](https://github.com/Moonofweisheng/wot-design-uni/commit/92db0f2c7fb60b04e1562c55f6296b1b2b456144))

### [0.1.25](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.24...v0.1.25) (2023-10-10)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Silder 滑块组件双向滑动时自定义最大值小于100无法滚动到最大值的问题 ([a70afa4](https://github.com/Moonofweisheng/wot-design-uni/commit/a70afa43239bc233ee0250e26227dbacd5c7021b)), closes [#69](https://github.com/Moonofweisheng/wot-design-uni/issues/69)

### [0.1.24](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.23...v0.1.24) (2023-10-10)


### 🐛 Bug Fixes | Bug 修复

* 🐛 优化 Badge 徽标组件超出max的显示并修复分段器组件错误依赖的问题 ([a48adc8](https://github.com/Moonofweisheng/wot-design-uni/commit/a48adc8395ab167bb88a7a647420b467d7e4c46d))

### [0.1.23](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.22...v0.1.23) (2023-09-28)


### ✨ Features | 新功能

* ✨ 增加 segmented 分段器组件 ([00f52c8](https://github.com/Moonofweisheng/wot-design-uni/commit/00f52c89e0aebc86f70aa8a7391ce1f17412333d))

### [0.1.22](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.21...v0.1.22) (2023-09-25)


### ✨ Features | 新功能

* ✨ 新增 Swiper 轮播图组件 ([cf9fe98](https://github.com/Moonofweisheng/wot-design-uni/commit/cf9fe9807efe863e786bc56eedf506647ac143d2))

### [0.1.21](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.20...v0.1.21) (2023-09-22)

### [0.1.20](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.19...v0.1.20) (2023-09-20)


### ✏️ Documentation | 文档

* ✏️  恢复展示 Circle 组件的演示Demo ([c2cba05](https://github.com/Moonofweisheng/wot-design-uni/commit/c2cba056578593172998f03592652ab63ed23aef))
* ✏️  文档演示项目 Circle 页面文件调整为大写 ([fff8de8](https://github.com/Moonofweisheng/wot-design-uni/commit/fff8de8f8d5f829c5b054d863905a7bb116f45f4))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Circle 组件重复创建canvas导致警告的问题 ([6917201](https://github.com/Moonofweisheng/wot-design-uni/commit/69172016ba840458dd40755050929a8231fd4cd5))
* 🐛 修复 Skeleton 骨架屏组件编译到APP端异常的问题([#52](https://github.com/Moonofweisheng/wot-design-uni/issues/52)) ([7a9a31b](https://github.com/Moonofweisheng/wot-design-uni/commit/7a9a31bd5db76419408a114fbed02c108b972049))
* 🐛 修复 Tag 组件编译到微信小程序平台样式错误的问题([#53](https://github.com/Moonofweisheng/wot-design-uni/issues/53)) ([6aec241](https://github.com/Moonofweisheng/wot-design-uni/commit/6aec2414a384c68e7b4b09f57d028173be942f7b))

### [0.1.19](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.18...v0.1.19) (2023-09-20)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Cell、Grid 组件跳转方法在编译到H5端失效的问题([#49](https://github.com/Moonofweisheng/wot-design-uni/issues/49)) ([51425b1](https://github.com/Moonofweisheng/wot-design-uni/commit/51425b107a8b262bf9c1f0c7ee41bb8d56a12837))


### ✨ Features | 新功能

* ✨ 新增 Circle 环形进度条组件，支持进度渐变动画 ([7c38a6f](https://github.com/Moonofweisheng/wot-design-uni/commit/7c38a6f03e76d91d82a80d2a4cdd875acfb7290b))

### [0.1.18](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.17...v0.1.18) (2023-09-19)


### ✏️ Documentation | 文档

* ✏️  README中增加展示 star-history ([e7cd794](https://github.com/Moonofweisheng/wot-design-uni/commit/e7cd794201435ae8109b562daf70def74ab4ac98))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 PickerView 组件选中框被遮盖的问题([#46](https://github.com/Moonofweisheng/wot-design-uni/issues/46)) ([a60440b](https://github.com/Moonofweisheng/wot-design-uni/commit/a60440b8c52723e4c194b103e9b1b236a7cb29fe))

### [0.1.17](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.16...v0.1.17) (2023-09-19)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复 Slider 滑块组件最大值和最小值不生效的问题([#43](https://github.com/Moonofweisheng/wot-design-uni/issues/43)) ([0fa7f46](https://github.com/Moonofweisheng/wot-design-uni/commit/0fa7f46718e918007491a3d9494292a003924243))

### [0.1.16](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.15...v0.1.16) (2023-09-18)


### ✨ Features | 新功能

* ✨ 新增 WaterMark 水印组件 ([70d8c25](https://github.com/Moonofweisheng/wot-design-uni/commit/70d8c2546135fce1709edb0e1ba0c3b66c1e9e2e))

### [0.1.15](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.14...v0.1.15) (2023-09-18)


### ✏️ Documentation | 文档

* ✏️  优化DateTimePicker组件关于time类型选择器绑定值格式的介绍 ([9e958c7](https://github.com/Moonofweisheng/wot-design-uni/commit/9e958c73f1d09ee0e02097e3ca8eeabfcc1bc59b))

### [0.1.14](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.13...v0.1.14) (2023-09-14)


### ✏️ Documentation | 文档

* ✏️  增加QQ群沟通渠道 ([80efad9](https://github.com/Moonofweisheng/wot-design-uni/commit/80efad950613d65e7e65d305ad6efe7cd0e201d8))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复SelectPicker 单复选选择器单选可搜索状态搜索报错的问题([#38](https://github.com/Moonofweisheng/wot-design-uni/issues/38)) ([01cf01d](https://github.com/Moonofweisheng/wot-design-uni/commit/01cf01d46fbc4ac35f0c09e3db6f6d18b2d3455e))

### [0.1.13](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.12...v0.1.13) (2023-09-12)


### ✨ Features | 新功能

* ✨ Tabs组件增加animated属性支持切换动画 ([2572ea4](https://github.com/Moonofweisheng/wot-design-uni/commit/2572ea4c31f834bb9c8776322c24148ca2bda4e2))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Popover组件content属性必填警告的问题 ([4d8f8e6](https://github.com/Moonofweisheng/wot-design-uni/commit/4d8f8e640742b0588b91b0a5b82a062b976f8306))

### [0.1.12](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.11...v0.1.12) (2023-09-12)


### ✏️ Documentation | 文档

* ✏️  修复文档中定制主题和反馈组件404的问题 ([c0302ad](https://github.com/Moonofweisheng/wot-design-uni/commit/c0302ada89ea15039ef516f4a425b5e18512c2d5))
* ✏️  修复Button组件文档中loading属性重复的问题 ([b26da5a](https://github.com/Moonofweisheng/wot-design-uni/commit/b26da5aba7d698adc75c3e1d143660a698ce5de8))

### [0.1.11](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.10...v0.1.11) (2023-09-08)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复文档中部分页面存在死链问题 ([b3e5cbe](https://github.com/Moonofweisheng/wot-design-uni/commit/b3e5cbef1116d9a356f7c9c18dffc1caec20ea22))

### [0.1.10](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.9...v0.1.10) (2023-09-08)


### ✏️ Documentation | 文档

* ✏️  将驼峰命名的文档文件改为短横线命名便于爬虫爬取及搜索 ([497e991](https://github.com/Moonofweisheng/wot-design-uni/commit/497e991903559c9d62dcb1b00842ee377d70b445))
* ✏️  优化icon组件文档的体验，支持点击图标复制使用示例 ([ce0526b](https://github.com/Moonofweisheng/wot-design-uni/commit/ce0526b99dcd203d3f1081a8fd6a0551da88b9b8))

### [0.1.9](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.8...v0.1.9) (2023-09-08)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复npm包未配置主入口导致在使用Hbx创建的项目中通过npm安装编译警告的问题 ([fa95a0d](https://github.com/Moonofweisheng/wot-design-uni/commit/fa95a0d19b6e13157154405ddaa2525545bd9f7e))

### [0.1.8](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.7...v0.1.8) (2023-09-07)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复ActionSheet等组件在H5端隐藏导航栏或在tabbar页面高度计算错误的问题 ([39201cb](https://github.com/Moonofweisheng/wot-design-uni/commit/39201cbeddde1c7ea883ad331c0607bced755475))
* 🐛 修复pmpm安装时ts导入类型信息在H5平台报错的问题 ([237c03c](https://github.com/Moonofweisheng/wot-design-uni/commit/237c03c6ae8d041c565ca6bb984765be4418f9e1))

### [0.1.7](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.6...v0.1.7) (2023-09-06)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复tabs组件change事件执行两次的问题 ([819059a](https://github.com/Moonofweisheng/wot-design-uni/commit/819059abe19fcf8f9646703fcc7a472b6ae62d4e))

### [0.1.6](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.5...v0.1.6) (2023-09-06)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Input组件内置变量showClear和showWordCount定义为props的问题 ([8021a35](https://github.com/Moonofweisheng/wot-design-uni/commit/8021a35ac9d05d549909531ff4f1c7325127833e))
* 🐛 修复pnpm安装时运行到h5平台Ref导入报错的问题 ([39c68bf](https://github.com/Moonofweisheng/wot-design-uni/commit/39c68bf57720c1a12b99412c96e46b341cf536b5))

### [0.1.5](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.4...v0.1.5) (2023-09-05)


### ✏️ Documentation | 文档

* ✏️  增加Stickty和Tabs组件关于H5端自定义导航栏的offset-top的处理方案 ([8a03c4d](https://github.com/Moonofweisheng/wot-design-uni/commit/8a03c4da64296e88a7b3aff5efed4ac04cd039b6))

### [0.1.4](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.3...v0.1.4) (2023-09-05)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复CollapseItem组件在微信小程序中展开/收起时指向图标未跟随转动的问题 ([7d0eeae](https://github.com/Moonofweisheng/wot-design-uni/commit/7d0eeae92f2f7b7664a23f7d548187ed8075d5b0))

### [0.1.3](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.2...v0.1.3) (2023-09-03)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复通过npm安装时配置自动导入Notify、Toast、Message组件无法打开的问题 ([f45b739](https://github.com/Moonofweisheng/wot-design-uni/commit/f45b73907227c25d9c2fd7b7f0018cc2ab5c47d1))

### [0.1.2](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.1...v0.1.2) (2023-09-03)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复部分js引入路径问题 ([67cdfae](https://github.com/Moonofweisheng/wot-design-uni/commit/67cdfaebf9478e286758b2ef851e1a44dc8565a6))

### [0.1.1](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.1.0...v0.1.1) (2023-09-02)

## [0.1.0](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.14...v0.1.0) (2023-09-02)


### ✏️ Documentation | 文档

* ✏️  ConfigProvider组件演示页面增加手动切换暗黑模式 ([e5f55c7](https://github.com/Moonofweisheng/wot-design-uni/commit/e5f55c72fc1ed6e603f22e501d6cff9d8212a976))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Grid组件不展示border的问题 ([819bbbc](https://github.com/Moonofweisheng/wot-design-uni/commit/819bbbca6ab1c999096936da5e9d2dd664e480ce))
* 🐛 修复MessageBox组件取消按钮不展示的问题 ([d8563d8](https://github.com/Moonofweisheng/wot-design-uni/commit/d8563d833d75b27d0c497c6c945fae8c00ef8dc7))

### [0.0.14](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.13...v0.0.14) (2023-09-02)


### ✨ Features | 新功能

* ✨ 优化缺省状态组件StautsTip，增加支持自定义url和图片大小 ([f463d32](https://github.com/Moonofweisheng/wot-design-uni/commit/f463d3258a954e64352df36004d34b0f12be9a8f))


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复util中addUnit无法处理有单位的字符串的问题 ([8967540](https://github.com/Moonofweisheng/wot-design-uni/commit/8967540ad0f3f0ad7426ee79571cfc4dee6c4d0c))


### ✏️ Documentation | 文档

* ✏️  完善和修复文档中不详细不正确的内容 ([d1b118e](https://github.com/Moonofweisheng/wot-design-uni/commit/d1b118ea78a797499f465269d3a838c0770d993a))

### [0.0.13](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.12...v0.0.13) (2023-09-01)


### ✏️ Documentation | 文档

* ✏️  官网地址迁移至阿里云oss ([a98868a](https://github.com/Moonofweisheng/wot-design-uni/commit/a98868ae6213c7d7002e5a88893aabe1d0f5d11b))

### [0.0.12](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.11...v0.0.12) (2023-08-30)

### [0.0.11](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.10...v0.0.11) (2023-08-30)


### ✨ Features | 新功能

* ✨ util工具类提供更好的类型提示 ([4fed439](https://github.com/Moonofweisheng/wot-design-uni/commit/4fed43926f49be6a86ebab54bb36a1a086df4ac6))

### [0.0.10](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.9...v0.0.10) (2023-08-27)


### ✨ Features | 新功能

* ✨ 新增Notify组件、演示demo、文档 ([#9](https://github.com/Moonofweisheng/wot-design-uni/issues/9)) ([996fc39](https://github.com/Moonofweisheng/wot-design-uni/commit/996fc39d708699214faf2e54224b8d38a8a706f7))
* ✨ 新增Skeleton组件、演示demo、文档 ([#12](https://github.com/Moonofweisheng/wot-design-uni/issues/12)) ([a49e22c](https://github.com/Moonofweisheng/wot-design-uni/commit/a49e22c370163ec3c93bbeb360cc89b3b3c0abc0))

### [0.0.9](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.8...v0.0.9) (2023-08-25)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复Cell 单格组件 is-link 不触发 click ([ece5568](https://github.com/Moonofweisheng/wot-design-uni/commit/ece5568ecc03fd8470c2c02120b94c1ee27d55e7))

### [0.0.8](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.7...v0.0.8) (2023-08-24)


### ✨ Features | 新功能

* ✨ 演示项目新增用户隐私保护指引的处理 ([4dd7efe](https://github.com/Moonofweisheng/wot-design-uni/commit/4dd7efe4049c4c815ea1bc57a7fd1819055f10bc))
* ✨ ActionSheet组件调整为使用v-model设置显示与隐藏 ([aa92332](https://github.com/Moonofweisheng/wot-design-uni/commit/aa92332f3913be000d1aef36a8aed7f34b736ad6))

### [0.0.7](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.6...v0.0.7) (2023-08-23)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复部分组件某些属性必填警告的问题 ([1b866c0](https://github.com/Moonofweisheng/wot-design-uni/commit/1b866c062eb7a4ab894d5b2ecd7b7b3fd50ef864))

### [0.0.6](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.5...v0.0.6) (2023-08-22)

### [0.0.5](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.4...v0.0.5) (2023-08-22)

### [0.0.4](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.3...v0.0.4) (2023-08-21)


### ✨ Features | 新功能

* ✨ Picker组件优化性能 ([24dd43f](https://github.com/Moonofweisheng/wot-design-uni/commit/24dd43f3a05b7b4ce6bb897219a215f8198e64ac))

### [0.0.3](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.2...v0.0.3) (2023-08-18)

### [0.0.2](https://github.com/Moonofweisheng/wot-design-uni/compare/v0.0.1...v0.0.2) (2023-08-18)


### 🐛 Bug Fixes | Bug 修复

* 🐛 修复CheckBoxGroup组件的disabled属性作用在子组件上失效的问题 ([1bab820](https://github.com/Moonofweisheng/wot-design-uni/commit/1bab820c0335c89e099c597caa47af16bb998d83))

### 0.0.1 (2023-08-16)


### ✨ Features | 新功能

* ✨ 适配暗黑模式 ([f5946a4](https://github.com/Moonofweisheng/wot-design-uni/commit/f5946a4b7134fed161bc123d66485e7bc91cdc68))
* ✨ 新增 50+ 组件
