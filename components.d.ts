/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    DemoBlock: typeof import('./src/components/demo-block/demo-block.vue')['default']
    PageWraper: typeof import('./src/components/page-wraper/page-wraper.vue')['default']
    WdPrivacyPopup: typeof import('./src/components/wd-privacy-popup/wd-privacy-popup.vue')['default']
  }
}
