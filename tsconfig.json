{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"verbatimModuleSyntax": false, "ignoreDeprecations": "5.0", "lib": ["ESNext", "DOM", "DOM.Iterable"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["node", "@dcloudio/types", "mini-types", "@uni-helper/uni-types", "miniprogram-api-typings", "@types/node", "vitest/jsdom"], "sourceMap": true, "module": "ESNext"}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-types/volar-plugin"]}, "exclude": ["node_modules", "unpackage", "src/**/*.nvue"]}